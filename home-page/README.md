# Personal Portfolio

A modern, responsive portfolio website built with Vue 3, TypeScript, and Tailwind CSS. Features a beautiful gradient theme system, internationalization support, and smooth animations.

![Portfolio Preview](https://image.xwysyy.cn/img-icon/%E5%A4%B4%E5%83%8F.jpg)

## Features

- 🎨 **Dynamic Theme System**
  - Beautiful gradient themes
  - Real-time theme switching
  - Theme persistence

- 🌐 **Internationalization**
  - English and Chinese language support
  - YAML-based translations
  - Automatic language detection

- 📱 **Responsive Design**
  - Mobile-first approach
  - Smooth animations
  - Optimized for all devices

- 🛠 **Technical Stack**
  - Vue 3 with Composition API
  - TypeScript for type safety
  - Tailwind CSS for styling
  - Vite for fast development
  - YAML for content management

## Project Structure

```
src/
├── components/      # Reusable Vue components
├── data/           # YAML content files
├── i18n/           # Internationalization setup
├── stores/         # Pinia stores
├── views/          # Page components
└── styles/         # Global styles
```

## Getting Started

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/portfolio.git
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Build for production:
   ```bash
   npm run build
   ```

## Content Management

All content is managed through YAML files in the `src/data` directory:

- `basic.yaml`: Basic information and about me
- `skills.yaml`: Skills and technologies
- `projects.yaml`: Project showcase
- `academic.yaml`: Academic achievements
- `contact.yaml`: Contact information
- `i18n.yaml`: Translations

## Customization

1. **Theme System**
   - Add new themes in `ColorPicker.vue`
   - Customize gradient colors
   - Modify transition effects

2. **Content**
   - Update YAML files in `src/data`
   - Add new sections as needed
   - Modify existing components

3. **Styling**
   - Tailwind configuration in `tailwind.config.js`
   - Global styles in `src/style.css`
   - Component-specific styles

## License

MIT License - feel free to use this project for your own portfolio!

## Contact

- Email: <EMAIL>
- GitHub: [github.com/xwysyy](https://github.com/xwysyy)