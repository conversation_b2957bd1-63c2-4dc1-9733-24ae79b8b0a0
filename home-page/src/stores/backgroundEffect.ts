import { defineStore } from 'pinia'
import { ref } from 'vue'
import { STORAGE_KEYS } from '../utils/constants'

export type BackgroundEffectType = 'none' | 'particles' | 'waves' | 'matrix' | 'bubbles' | 'fireflies'

export const useBackgroundEffectStore = defineStore('backgroundEffect', () => {
  // 迁移旧的粒子设置到新的效果系统
  const migrateFromOldParticleSettings = (): { type: BackgroundEffectType, enabled: boolean } => {
    const oldParticlesEnabled = localStorage.getItem(STORAGE_KEYS.PARTICLES)
    
    // 如果已经有新设置，则使用新设置
    const existingType = localStorage.getItem(STORAGE_KEYS.EFFECT_TYPE) as BackgroundEffectType | null
    const existingEnabled = localStorage.getItem(STORAGE_KEYS.EFFECT_ENABLED)
    
    if (existingType && existingEnabled !== null) {
      return {
        type: existingType,
        enabled: existingEnabled === 'true'
      }
    }
    
    // 从旧设置迁移
    // 如果旧系统中粒子是启用的，则设置为particles类型并启用
    // 如果旧系统中粒子是禁用的，则设置为none类型
    if (oldParticlesEnabled !== 'false') {
      return {
        type: 'particles',
        enabled: true
      }
    } else {
      return {
        type: 'none',
        enabled: false
      }
    }
  }
  
  // 初始化状态，包括从旧系统迁移
  const initialState = migrateFromOldParticleSettings()
  
  // 从localStorage获取特效类型，考虑迁移情况
  const effectType = ref<BackgroundEffectType>(initialState.type)
  
  // 特效是否启用
  const enabled = ref(initialState.enabled)
  
  // 切换特效开关
  function toggleEffect() {
    enabled.value = !enabled.value
    localStorage.setItem(STORAGE_KEYS.EFFECT_ENABLED, enabled.value.toString())
  }
  
  // 启用特效
  function enableEffect() {
    enabled.value = true
    localStorage.setItem(STORAGE_KEYS.EFFECT_ENABLED, 'true')
  }
  
  // 禁用特效
  function disableEffect() {
    enabled.value = false
    localStorage.setItem(STORAGE_KEYS.EFFECT_ENABLED, 'false')
  }
  
  // 设置特效类型
  function setEffectType(type: BackgroundEffectType) {
    effectType.value = type
    localStorage.setItem(STORAGE_KEYS.EFFECT_TYPE, type)
    
    // 如果选择了非none类型的特效，自动启用
    if (type !== 'none' && !enabled.value) {
      enableEffect()
    }
    
    // 如果选择了none类型，自动禁用
    if (type === 'none' && enabled.value) {
      disableEffect()
    }
  }
  
  return {
    effectType,
    enabled,
    toggleEffect,
    enableEffect,
    disableEffect,
    setEffectType
  }
})
