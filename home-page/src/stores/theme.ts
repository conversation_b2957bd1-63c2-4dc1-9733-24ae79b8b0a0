import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { STORAGE_KEYS, DEFAULT_GRADIENT } from '../utils/constants'
import type { GradientTheme } from '../types/theme'

export const useThemeStore = defineStore('theme', () => {
  const currentGradient = ref<GradientTheme>(DEFAULT_GRADIENT)
  
  // 新增 - 跟踪是否为自定义主题
  const isCustomTheme = ref(false)
  
  // 新增 - 计算背景样式
  const backgroundStyle = computed(() => {
    if (isCustomTheme.value) {
      return {
        backgroundImage: `linear-gradient(to bottom right, ${currentGradient.value.from}66, ${currentGradient.value.via}66, ${currentGradient.value.to}66)`,
      }
    }
    return {}
  })
  
  // 加载主题
  function loadTheme() {
    const savedTheme = localStorage.getItem(STORAGE_KEYS.THEME)
    if (savedTheme) {
      try {
        const theme = JSON.parse(savedTheme) as GradientTheme
        currentGradient.value = theme
        // 检查是否为自定义主题
        isCustomTheme.value = !!theme.isCustom
      } catch (e) {
        console.error('Failed to parse saved theme', e)
        currentGradient.value = DEFAULT_GRADIENT
        isCustomTheme.value = false
      }
    }
  }
  
  // 保存主题
  function saveTheme(theme: GradientTheme) {
    currentGradient.value = theme
    // 设置是否为自定义主题标志
    isCustomTheme.value = !!theme.isCustom
    localStorage.setItem(STORAGE_KEYS.THEME, JSON.stringify(theme))
  }
  
  return {
    currentGradient,
    isCustomTheme,
    backgroundStyle,
    loadTheme,
    saveTheme
  }
})