import { computed } from 'vue'
import { useThemeStore } from '../stores/theme'
import type { GradientTheme } from '../types/theme'

export const useTheme = () => {
  const themeStore = useThemeStore()

  const currentTheme = computed(() => themeStore.currentGradient)

  const setTheme = (theme: GradientTheme) => {
    themeStore.saveTheme(theme)
  }

  const gradientClasses = computed(() => [
    themeStore.currentGradient.from,
    themeStore.currentGradient.via,
    themeStore.currentGradient.to
  ])

  return {
    currentTheme,
    setTheme,
    gradientClasses
  }
}