import { computed } from 'vue'
import { useI18n as useVueI18n } from 'vue-i18n'
import { useLangStore } from '../stores/lang'

export const useI18n = () => {
  const { t, locale } = useVueI18n()
  const langStore = useLangStore()

  const isZh = computed(() => locale.value === 'zh')
  const isEn = computed(() => locale.value === 'en')

  const toggleLang = () => {
    const newLang = isZh.value ? 'en' : 'zh'
    locale.value = newLang
    langStore.setLang(newLang)
  }

  return {
    t,
    locale,
    isZh,
    isEn,
    toggleLang
  }
}