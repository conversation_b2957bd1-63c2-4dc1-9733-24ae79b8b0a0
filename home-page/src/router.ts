import { createRouter, createWebHistory } from 'vue-router'
import Home from './views/Home.vue'
import Projects from './views/Projects.vue'
import Blog from './views/Blog.vue'
import Academic from './views/Academic.vue'
import Contact from './views/Contact.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home
    },
    {
      path: '/projects',
      name: 'projects',
      component: Projects
    },
    {
      path: '/blog',
      name: 'blog',
      component: Blog
    },
    {
      path: '/academic',
      name: 'academic',
      component: Academic
    },
    {
      path: '/contact',
      name: 'contact',
      component: Contact
    }
  ]
})

// Handle route restoration on page refresh
if (typeof window !== 'undefined') {
  const savedPath = localStorage.getItem('path');
  if (savedPath) {
    router.push(savedPath).then(() => {
      localStorage.removeItem('path');
    });
  }
}

export default router