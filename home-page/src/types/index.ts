// Basic Info Types
export interface BasicInfo {
  name: string
  avatar: string
  titleZh: string
  titleEn: string
  descriptionZh: string
  descriptionEn: string
  aboutZh: {
    content1: string
    content2: string
  }
  aboutEn: {
    content1: string
    content2: string
  }
}

// Skills Types
export interface Skill {
  name: string
  icon: string
  category: string
}

// Project Types
export interface Project {
  title: string
  titleEn: string
  description: string
  descriptionEn: string
  tech: string[]
  image: string
  icons: string[]
}

// Academic Types
export interface Paper {
  title: string
  titleEn: string
  authors: string[]
  conference: string
  abstract: string
  abstractEn: string
  keywords: string[]
  keywordsEn: string[]
  year: number
}

// Contact Types
export interface Contact {
  type: string
  icon: string
  titleZh: string
  titleEn: string
  value: string
  link: string
}

// i18n Types
export interface I18nMessages {
  [key: string]: {
    nav: {
      home: string
      projects: string
      blog: string
      academic: string
      contact: string
    }
    common: {
      greeting: string
      aboutMe: string
      skills: string
      skillCategories: {
        all: string
        frontend: string
        backend: string
        database: string
        devops: string
        api: string
        vcs: string
        testing: string
        mobile: string
        tools: string
      }
    }
    projects: {
      title: string
    }
    blog: {
      title: string
    }
    academic: {
      title: string
    }
    contact: {
      title: string
    }
  }
}