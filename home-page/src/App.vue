<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'
import { useI18n } from 'vue-i18n'
import { useThemeStore } from './stores/theme'
import ColorPicker from './components/ColorPicker.vue'
import ParticleBackground from './components/common/ParticleBackground.vue'
import WaveBackground from './components/common/WaveBackground.vue'
import MatrixBackground from './components/common/MatrixBackground.vue'
import BubblesBackground from './components/common/BubblesBackground.vue'
import FirefliesBackground from './components/common/FirefliesBackground.vue'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import type { BasicInfo } from './types'
import { loadYamlData } from './utils/loadYaml'

const router = useRouter()
const { t } = useI18n()
const themeStore = useThemeStore()
const isMobileMenuOpen = ref(false)
const basicInfo = ref<BasicInfo | null>(null)
const avatarError = ref(false)

const navItems = [
  { name: 'nav.home', icon: 'mdi:home', route: '/' },
  { name: 'nav.projects', icon: 'mdi:rocket', route: '/projects' },
  { name: 'nav.blog', icon: 'mdi:book-open', route: '/blog' },
  { name: 'nav.academic', icon: 'mdi:school', route: '/academic' },
  { name: 'nav.contact', icon: 'mdi:email', route: '/contact' }
]

const loadBasicInfo = async () => {
  try {
    basicInfo.value = await loadYamlData<BasicInfo>('basic.yaml')
  } catch (error) {
    console.error('Error loading basic info:', error)
  }
}

// Configure NProgress
NProgress.configure({ 
  showSpinner: false,
  easing: 'ease',
  speed: 500
})

// Add router guards for NProgress
router.beforeEach((to, from, next) => {
  if (to.path !== from.path) {
    NProgress.start()
  }
  next()
})

router.afterEach(() => {
  NProgress.done()
  // Close mobile menu when route changes
  isMobileMenuOpen.value = false
})

onMounted(() => {
  themeStore.loadTheme()
  loadBasicInfo()
})
</script>

<template>
  <div class="min-h-screen text-white">
    <!-- 背景层：使用动态样式或Tailwind类 -->
    <div 
      class="fixed inset-0 -z-20 bg-gradient-to-br transition-colors duration-700"
      :class="[!themeStore.isCustomTheme ? [themeStore.currentGradient.from, themeStore.currentGradient.via, themeStore.currentGradient.to] : '']"
      :style="themeStore.isCustomTheme ? themeStore.backgroundStyle : {}"
    >
      <div class="absolute inset-0 bg-black/20"></div>
    </div>
    
    <!-- 背景特效层：降低z-index确保在内容之下 -->
    <div class="fixed inset-0 -z-10 overflow-hidden">
      <ParticleBackground />
      <WaveBackground />
      <MatrixBackground />
      <BubblesBackground />
      <FirefliesBackground />
    </div>
    
    <!-- 顶部导航栏 -->
    <nav class="fixed top-0 left-0 right-0 bg-white/5 backdrop-blur-lg border-b border-white/10 z-50">
      <div class="container mx-auto px-4">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center gap-2" v-if="basicInfo">
            <div class="w-8 h-8 rounded-full overflow-hidden bg-white/10">
              <img 
                v-if="!avatarError"
                :src="basicInfo.avatar" 
                :alt="basicInfo.name" 
                class="w-full h-full object-cover"
                @error="avatarError = true"
              />
              <Icon 
                v-else
                icon="mdi:account"
                class="w-full h-full text-white/50"
              />
            </div>
            <div class="text-xl font-bold text-white">{{ basicInfo.name }}</div>
          </div>
          
          <!-- Desktop Navigation -->
          <div class="hidden md:flex items-center space-x-6">
            <router-link 
              v-for="item in navItems" 
              :key="item.name"
              :to="item.route"
              class="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-white/10 transition-colors duration-200 text-white"
              active-class="bg-white/10"
            >
              <Icon :icon="item.icon" class="w-5 h-5" />
              <span>{{ t(item.name) }}</span>
            </router-link>
          </div>

          <!-- Mobile Menu Button -->
          <button 
            class="md:hidden p-2 rounded-lg hover:bg-white/10 transition-colors duration-200 text-white"
            @click="isMobileMenuOpen = !isMobileMenuOpen"
          >
            <Icon 
              :icon="isMobileMenuOpen ? 'mdi:close' : 'mdi:menu'" 
              class="w-6 h-6"
            />
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <div 
        v-show="isMobileMenuOpen"
        class="md:hidden bg-white/5 backdrop-blur-lg border-b border-white/10"
      >
        <div class="container mx-auto px-4 py-2">
          <router-link 
            v-for="item in navItems" 
            :key="item.name"
            :to="item.route"
            class="flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-white/10 transition-colors duration-200 mb-2 last:mb-0 text-white"
            active-class="bg-white/10"
            @click="isMobileMenuOpen = false"
          >
            <Icon :icon="item.icon" class="w-5 h-5" />
            <span>{{ t(item.name) }}</span>
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="pt-16 relative z-10">
      <router-view v-slot="{ Component }">
        <transition 
          name="fade-up"
          mode="out-in"
        >
          <component :is="Component" />
        </transition>
      </router-view>
    </div>

    <!-- 设置面板 -->
    <ColorPicker />
  </div>
</template>

<style>
/* 自定义 NProgress 样式 */
#nprogress .bar {
  background: linear-gradient(45deg, rgba(219, 234, 254, 0.8), rgba(255, 255, 255, 0.8)) !important;
  height: 3px !important;
}

#nprogress .peg {
  box-shadow: 0 0 10px rgba(219, 234, 254, 0.8), 0 0 5px rgba(255, 255, 255, 0.8) !important;
}

/* Mobile menu transition */
.mobile-menu-enter-active,
.mobile-menu-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.mobile-menu-enter-from,
.mobile-menu-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 页面过渡动画 */
.fade-up-enter-active,
.fade-up-leave-active {
  transition: all 0.5s ease;
}

.fade-up-enter-from,
.fade-up-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
</style>