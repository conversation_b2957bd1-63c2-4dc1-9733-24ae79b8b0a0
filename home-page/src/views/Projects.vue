<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Icon } from '@iconify/vue'
import { useI18n } from 'vue-i18n'
import type { Project } from '../types'
import { loadYamlData } from '../utils/loadYaml'

const { t, locale } = useI18n()
const projects = ref<Project[]>([])

const loadProjects = async () => {
  try {
    projects.value = await loadYamlData<Project[]>('projects.yaml')
  } catch (error) {
    console.error('Error loading projects:', error)
  }
}

onMounted(() => {
  loadProjects()
})
</script>

<template>
  <div class="container mx-auto px-4 py-12">
    <h1 class="text-5xl font-bold mb-12 text-center text-white">{{ t('projects.title') }}</h1>
    
    <div class="space-y-8">
      <div 
        v-for="project in projects" 
        :key="project.title"
        class="group relative overflow-hidden rounded-xl bg-white/5 backdrop-blur-lg hover:bg-white/10 transition-all duration-500"
      >
        <div class="flex flex-col md:flex-row">
          <div class="w-full md:w-1/3">
            <div class="aspect-video overflow-hidden">
              <img 
                :src="project.image" 
                :alt="locale === 'zh' ? project.title : project.titleEn"
                class="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-500"
              />
            </div>
          </div>
          <div class="w-full md:w-2/3 p-6">
            <h3 class="text-2xl font-bold mb-2 text-white">
              {{ locale === 'zh' ? project.title : project.titleEn }}
            </h3>
            <p class="text-white/90 mb-4">
              {{ locale === 'zh' ? project.description : project.descriptionEn }}
            </p>
            <div class="flex items-center gap-4">
              <Icon 
                v-for="icon in project.icons"
                :key="icon"
                :icon="icon"
                class="w-8 h-8 text-white"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>