<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Icon } from '@iconify/vue'
import type { Skill, BasicInfo } from '../types'
import { loadYamlData } from '../utils/loadYaml'

const target = ref(null)
const { t, locale } = useI18n()
const skills = ref<Skill[]>([])
const basicInfo = ref<BasicInfo | null>(null)
const selectedCategory = ref('all')
const avatarError = ref(false)

const categories = computed(() => {
  if (!skills.value.length) return []
  const cats = ['all', ...new Set(skills.value.map(skill => skill.category))]
  return cats.map(cat => ({
    value: cat,
    label: t(`common.skillCategories.${cat}`)
  }))
})

const filteredSkills = computed(() => {
  if (selectedCategory.value === 'all') return skills.value
  return skills.value.filter(skill => skill.category === selectedCategory.value)
})

const loadSkills = async () => {
  try {
    skills.value = await loadYamlData<Skill[]>('skills.yaml')
  } catch (error) {
    console.error('Error loading skills:', error)
  }
}

const loadBasicInfo = async () => {
  try {
    basicInfo.value = await loadYamlData<BasicInfo>('basic.yaml')
  } catch (error) {
    console.error('Error loading basic info:', error)
  }
}

onMounted(() => {
  loadSkills()
  loadBasicInfo()
})
</script>

<template>
  <div v-if="basicInfo">
    <!-- Hero Section -->
    <div ref="target" class="min-h-[calc(100vh-4rem)] flex items-center justify-center px-4">
      <div class="flex flex-col items-center">
        <div class="avatar-container">
          <div class="avatar-border"></div>
          <div class="avatar-glow"></div>
          <div class="w-32 h-32 md:w-48 md:h-48 rounded-full overflow-hidden relative z-10">
            <img 
              v-if="!avatarError"
              :src="basicInfo.avatar" 
              :alt="basicInfo.name"
              class="w-full h-full object-cover"
              @error="avatarError = true"
            />
            <Icon 
              v-else
              icon="mdi:account"
              class="w-full h-full text-white/50"
            />
          </div>
        </div>
        <h1 class="text-4xl md:text-7xl font-bold mb-4 text-center text-white">
          {{ t('common.greeting') }} {{ basicInfo.name }}
        </h1>
        <h2 class="text-2xl md:text-4xl font-semibold mb-6 text-center text-white/90">
          {{ locale === 'zh' ? basicInfo.titleZh : basicInfo.titleEn }}
        </h2>
        <p class="text-lg md:text-xl text-white mb-8 max-w-2xl text-center px-4">
          {{ locale === 'zh' ? basicInfo.descriptionZh : basicInfo.descriptionEn }}
        </p>
      </div>
    </div>

    <!-- 关于我 -->
    <div class="py-16 md:py-24 bg-white/5 backdrop-blur-lg">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl md:text-4xl font-bold mb-8 md:mb-12 text-center text-white">
          {{ t('common.aboutMe') }}
        </h2>
        <div class="max-w-4xl mx-auto">
          <p class="text-base md:text-lg text-white leading-relaxed mb-6 md:mb-8">
            {{ locale === 'zh' ? basicInfo.aboutZh.content1 : basicInfo.aboutEn.content1 }}
          </p>
          <p class="text-base md:text-lg text-white leading-relaxed">
            {{ locale === 'zh' ? basicInfo.aboutZh.content2 : basicInfo.aboutEn.content2 }}
          </p>
        </div>
      </div>
    </div>

    <!-- 技能专长 -->
    <div class="py-16 md:py-24">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl md:text-4xl font-bold mb-8 md:mb-12 text-center text-white">
          {{ t('common.skills') }}
        </h2>

        <!-- Category Filter -->
        <div class="flex flex-wrap justify-center gap-2 mb-8">
          <button
            v-for="category in categories"
            :key="category.value"
            @click="selectedCategory = category.value"
            class="px-4 py-2 rounded-full transition-all duration-300 text-white"
            :class="selectedCategory === category.value ? 
              'bg-white/20' : 
              'bg-white/5 hover:bg-white/10'"
          >
            {{ category.label }}
          </button>
        </div>

        <!-- Skills Grid -->
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 max-w-6xl mx-auto">
          <div 
            v-for="skill in filteredSkills" 
            :key="skill.name"
            class="flex flex-col items-center gap-2 p-4 bg-white/5 backdrop-blur-lg rounded-xl hover:bg-white/10 transition-all duration-300"
          >
            <div class="w-12 h-12 md:w-16 md:h-16 rounded-xl bg-white/10 flex items-center justify-center">
              <Icon :icon="skill.icon" class="w-8 h-8 md:w-10 md:h-10 text-white" />
            </div>
            <span class="text-sm md:text-base text-center text-white">{{ skill.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.avatar-container {
  position: relative;
  width: 8rem;
  height: 8rem;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .avatar-container {
    width: 12rem;
    height: 12rem;
  }
}

.avatar-border {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background: conic-gradient(
    from 0deg,
    #93c5fd,
    #60a5fa,
    #3b82f6,
    #60a5fa,
    #93c5fd
  );
  animation: rotate 4s linear infinite;
}

.avatar-glow {
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border-radius: 50%;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(147, 197, 253, 0.4),
    rgba(96, 165, 250, 0.4),
    rgba(59, 130, 246, 0.4),
    rgba(96, 165, 250, 0.4)
  );
  filter: blur(10px);
  animation: pulse 2s ease-in-out infinite;
  z-index: 1;
}

.avatar-border::before {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 1;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}
</style>