<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { MdPreview } from 'md-editor-v3'
import 'md-editor-v3/lib/style.css'
import type { Paper } from '../types'
import { loadYamlData } from '../utils/loadYaml'

const { t, locale } = useI18n()
const papers = ref<Paper[]>([])

const loadPapers = async () => {
  try {
    papers.value = await loadYamlData<Paper[]>('academic.yaml')
  } catch (error) {
    console.error('Error loading papers:', error)
  }
}

const previewConfig = {
  mermaid: false
}

onMounted(() => {
  loadPapers()
})
</script>

<template>
  <div class="container mx-auto px-4 py-12">
    <h1 class="text-5xl font-bold mb-12 text-center text-white">{{ t('academic.title') }}</h1>
    
    <div class="space-y-8">
      <div 
        v-for="paper in papers" 
        :key="paper.title"
        class="bg-white/5 backdrop-blur-lg rounded-xl p-6 hover:bg-white/10 transition-all duration-300"
      >
        <div class="flex items-start justify-between gap-4">
          <div>
            <h2 class="text-2xl font-bold mb-2 text-white">
              {{ locale === 'zh' ? paper.title : paper.titleEn }}
            </h2>
            <div class="text-sm text-white/80 mb-4">
              <div class="mb-1">{{ paper.authors.join(', ') }}</div>
              <div>{{ paper.conference }}</div>
            </div>
          </div>
          <div class="text-2xl font-bold text-white/80">
            {{ paper.year }}
          </div>
        </div>
        
        <div class="prose prose-invert max-w-none mb-4">
          <MdPreview 
            :modelValue="locale === 'zh' ? paper.abstract : paper.abstractEn"
            :preview-theme="'dark'"
            :show-code-row-number="false"
            :preview-options="previewConfig"
            class="!bg-transparent md-preview-custom"
          />
        </div>
        
        <div class="flex flex-wrap gap-2">
          <span 
            v-for="keyword in (locale === 'zh' ? paper.keywords : paper.keywordsEn)"
            :key="keyword"
            class="px-3 py-1 rounded-full text-sm bg-white/10 text-white hover:bg-white/20 transition-colors duration-300"
          >
            {{ keyword }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.md-preview-custom {
  --md-color: white !important;
  --md-bk-color: transparent !important;
}

.md-preview-custom :deep(*) {
  color: white !important;
}

.md-preview-custom :deep(p),
.md-preview-custom :deep(li),
.md-preview-custom :deep(ul),
.md-preview-custom :deep(ol) {
  color: rgba(255, 255, 255, 0.9) !important;
}

.md-preview-custom :deep(.katex) {
  color: white !important;
}

.md-preview-custom :deep(.katex *) {
  color: white !important;
  border-color: white !important;
}

.md-preview-custom :deep(ul),
.md-preview-custom :deep(ol) {
  padding-left: 1.5em;
  margin: 1em 0;
}

.md-preview-custom :deep(li) {
  margin: 0.5em 0;
}

.md-preview-custom :deep(h1),
.md-preview-custom :deep(h2),
.md-preview-custom :deep(h3),
.md-preview-custom :deep(h4),
.md-preview-custom :deep(h5),
.md-preview-custom :deep(h6) {
  color: white !important;
  font-weight: bold;
  margin: 1em 0 0.5em 0;
}
</style>