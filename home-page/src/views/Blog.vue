<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { Icon } from '@iconify/vue'

const { t } = useI18n()

interface Category {
  id: number
  name: string
}

interface Article {
  ID: number
  Category: Category
  CreatedAt: string
  UpdatedAt: string
  title: string
  desc: string
  img: string
  comment_count: number
  read_count: number
}

const articles = ref<Article[]>([])
const loading = ref(true)
const error = ref<string | null>(null)

const fetchArticles = async () => {
  try {
    loading.value = true
    const response = await fetch('https://blog-api.xwysyy.cn/api/v1/article?pagenum=1&pagesize=10')
    if (!response.ok) {
      throw new Error('Failed to fetch articles')
    }
    const data = await response.json()
    articles.value = data.data
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An error occurred'
  } finally {
    loading.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getArticleUrl = (id: number) => {
  return `https://www.xwysyy.cn/article/detail/${id}`
}

onMounted(() => {
  fetchArticles()
})
</script>

<template>
  <div class="container mx-auto px-4 py-12">
    <h1 class="text-5xl font-bold mb-12 text-center text-white">{{ t('blog.title') }}</h1>
    
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center min-h-[400px]">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center min-h-[400px] flex items-center justify-center">
      <div class="bg-red-500/20 backdrop-blur-lg rounded-lg p-6 max-w-md">
        <Icon icon="mdi:alert" class="w-12 h-12 text-red-400 mx-auto mb-4" />
        <p class="text-red-200">{{ error }}</p>
        <button 
          @click="fetchArticles" 
          class="mt-4 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors duration-300 text-white"
        >
          Retry
        </button>
      </div>
    </div>

    <!-- Articles List -->
    <div v-else class="space-y-8">
      <article 
        v-for="article in articles" 
        :key="article.ID"
        class="group bg-white/5 backdrop-blur-lg rounded-xl overflow-hidden hover:bg-white/10 transition-all duration-300"
      >
        <div class="flex flex-col md:flex-row">
          <div class="w-full md:w-1/3">
            <div class="aspect-video">
              <img 
                :src="article.img" 
                :alt="article.title"
                class="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-500"
              />
            </div>
          </div>
          <div class="w-full md:w-2/3 p-6">
            <div class="flex items-center gap-4 text-sm text-white/70 mb-3">
              <span>{{ formatDate(article.CreatedAt) }}</span>
              <span class="flex items-center gap-1">
                <Icon icon="mdi:eye" class="w-4 h-4" />
                {{ article.read_count }}
              </span>
              <span class="flex items-center gap-1">
                <Icon icon="mdi:comment" class="w-4 h-4" />
                {{ article.comment_count }}
              </span>
              <span class="px-2 py-1 rounded-full bg-white/10 text-xs text-white">
                {{ article.Category.name }}
              </span>
            </div>
            <h2 class="text-2xl font-bold mb-3 text-white group-hover:text-blue-400 transition-colors duration-300">
              {{ article.title }}
            </h2>
            <p class="text-white/90 mb-4">{{ article.desc }}</p>
            <a 
              :href="getArticleUrl(article.ID)"
              target="_blank"
              rel="noopener noreferrer"
              class="text-blue-400 hover:text-blue-300 transition-colors duration-300 flex items-center gap-2 group-hover:gap-3"
            >
              Read more
              <Icon icon="mdi:arrow-right" class="w-5 h-5 transition-transform duration-300" />
            </a>
          </div>
        </div>
      </article>
    </div>
  </div>
</template>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>