<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Icon } from '@iconify/vue'
import { useI18n } from 'vue-i18n'
import type { Contact } from '../types'
import { loadYamlData } from '../utils/loadYaml'

const { locale } = useI18n()
const contacts = ref<Contact[]>([])

const loadContacts = async () => {
  try {
    contacts.value = await loadYamlData<Contact[]>('contact.yaml')
  } catch (error) {
    console.error('Error loading contacts:', error)
  }
}

onMounted(() => {
  loadContacts()
})
</script>

<template>
  <div class="container mx-auto px-4 py-12">
    <div class="max-w-4xl mx-auto text-center">
      <h1 class="text-5xl font-bold mb-12 text-white">{{ locale === 'zh' ? '联系方式' : 'Contact' }}</h1>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-8">
        <div 
          v-for="contact in contacts" 
          :key="contact.type"
          class="bg-white/5 backdrop-blur-lg rounded-xl p-6 hover:bg-white/10 transform hover:scale-105 transition-all duration-300 group"
        >
          <a 
            :href="contact.link" 
            target="_blank" 
            rel="noopener noreferrer"
            class="flex flex-col items-center gap-4"
          >
            <div class="w-16 h-16 rounded-2xl bg-white/10 flex items-center justify-center group-hover:bg-white/20 transition-all duration-300">
              <Icon :icon="contact.icon" class="w-8 h-8 text-white" />
            </div>
            <div class="text-center">
              <h3 class="text-xl font-semibold mb-2 text-white">{{ locale === 'zh' ? contact.titleZh : contact.titleEn }}</h3>
              <p class="text-white/90">{{ contact.value }}</p>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>