<script setup lang="ts">
defineProps<{
  title: string
  date: string
  summary: string
  tags: string[]
}>()
</script>

<template>
  <article class="bg-white/10 backdrop-blur-lg rounded-lg p-6 hover:transform hover:scale-[1.02] transition-transform duration-300">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-xl font-semibold text-white">{{ title }}</h3>
      <span class="text-sm text-gray-400">{{ date }}</span>
    </div>
    <p class="text-gray-300 mb-4">{{ summary }}</p>
    <div class="flex flex-wrap gap-2">
      <span
        v-for="tag in tags"
        :key="tag"
        class="px-3 py-1 rounded-full text-sm bg-white/20 text-white"
      >
        {{ tag }}
      </span>
    </div>
  </article>
</template>