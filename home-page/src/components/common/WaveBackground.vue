<script setup lang="ts">
import { onMounted, onUnmounted, ref, watchEffect } from 'vue'
import { useBackgroundEffectStore } from '../../stores/backgroundEffect'

const canvas = ref<HTMLCanvasElement | null>(null)
let ctx: CanvasRenderingContext2D | null = null
let animationFrameId = 0
let waves: Wave[] = []
let hue = 180 // 起始色调

const backgroundEffectStore = useBackgroundEffectStore()

interface Wave {
  y: number
  length: number
  amplitude: number
  frequency: number
  speed: number
  lineWidth: number
  hueOffset: number
  opacity: number
}

// 配置选项
const config = {
  waveCount: 3,
  hueSpeed: 0.2,
  animate: true
}

const createWave = (index: number): Wave => {
  const opacity = 0.3 - (index * 0.1)
  return {
    y: 0,
    length: 200 + index * 50,
    amplitude: 40 + index * 20,
    frequency: 0.01 - (index * 0.002),
    speed: 0.05 + (index * 0.01),
    lineWidth: 2 + index * 0.5,
    hueOffset: index * 30,
    opacity: Math.max(0.05, opacity)
  }
}

const initWaves = () => {
  waves = Array.from({ length: config.waveCount }, (_, i) => createWave(i))
}

const drawWaves = () => {
  const canvasEl = canvas.value
  if (!canvasEl || !ctx) return
  
  ctx.clearRect(0, 0, canvasEl.width, canvasEl.height)
  
  // 更新色相
  if (config.animate) {
    hue = (hue + config.hueSpeed) % 360
  }
  
  waves.forEach((wave) => {
    ctx!.beginPath()
    
    const waveHue = (hue + wave.hueOffset) % 360
    ctx!.strokeStyle = `hsla(${waveHue}, 100%, 70%, ${wave.opacity})`
    ctx!.lineWidth = wave.lineWidth
    
    // 绘制波浪线
    for (let x = 0; x < canvasEl.width; x++) {
      // 计算y位置 (使用正弦函数)
      const y = (Math.sin(x * wave.frequency + wave.y) * wave.amplitude) + (canvasEl.height / 2)
      
      if (x === 0) {
        ctx!.moveTo(x, y)
      } else {
        ctx!.lineTo(x, y)
      }
    }
    
    ctx!.stroke()
    
    // 更新波浪位置
    wave.y += wave.speed
  })
  
  animationFrameId = requestAnimationFrame(drawWaves)
}

const handleResize = () => {
  const canvasEl = canvas.value
  if (!canvasEl) return
  canvasEl.width = window.innerWidth
  canvasEl.height = window.innerHeight
}

// 启动动画
const startAnimation = () => {
  if (!animationFrameId) {
    drawWaves()
  }
}

// 停止动画
const stopAnimation = () => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = 0
  }
}

// 清除画布
const clearCanvas = () => {
  const canvasEl = canvas.value
  if (!canvasEl || !ctx) return
  ctx.clearRect(0, 0, canvasEl.width, canvasEl.height)
}

// 监听背景特效开关状态
watchEffect(() => {
  if (backgroundEffectStore.enabled && backgroundEffectStore.effectType === 'waves') {
    startAnimation()
  } else {
    stopAnimation()
    clearCanvas()
  }
})

onMounted(() => {
  const canvasEl = canvas.value
  if (!canvasEl) return
  
  ctx = canvasEl.getContext('2d')
  if (!ctx) return

  handleResize()
  window.addEventListener('resize', handleResize)
  
  initWaves()
  
  // 只有在波浪特效启用且当前是波浪特效时才启动动画
  if (backgroundEffectStore.enabled && backgroundEffectStore.effectType === 'waves') {
    startAnimation()
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
  }
})
</script>

<template>
  <canvas
    ref="canvas"
    class="fixed inset-0 pointer-events-none z-0"
    :class="{ 'opacity-0': !backgroundEffectStore.enabled || backgroundEffectStore.effectType !== 'waves' }"
  />
</template>
