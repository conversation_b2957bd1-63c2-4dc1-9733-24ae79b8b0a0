<script setup lang="ts">
import { onMounted, onUnmounted, ref, watchEffect } from 'vue'
import { useBackgroundEffectStore } from '../../stores/backgroundEffect'

const canvas = ref<HTMLCanvasElement | null>(null)
let ctx: CanvasRenderingContext2D | null = null
let animationFrameId = 0
let columns: Column[] = []
const fontSize = 14
const fontFamily = 'monospace'

const backgroundEffectStore = useBackgroundEffectStore()

interface Column {
  x: number
  chars: string[]
  y: number
  speed: number
  opacity: number
  fadeSpeed: number
}

// 随机日语字符、中文字符、数字和特殊符号
const characters = () => {
  // 日语片假名和平假名区域
  const japaneseStart = 0x3040
  const japaneseEnd = 0x30FF
  
  // 少量中文字符区域
  const chineseStart = 0x4E00
  const chineseEnd = 0x9FFF
  
  // 数字0-9和一些特殊符号
  const numbers = '0123456789$%&*+=-_?><~'
  
  // 随机生成一个字符
  const randType = Math.random()
  if (randType < 0.5) {
    // 50% 日语字符
    return String.fromCharCode(
      Math.floor(Math.random() * (japaneseEnd - japaneseStart + 1) + japaneseStart)
    )
  } else if (randType < 0.8) {
    // 30% 中文字符
    return String.fromCharCode(
      Math.floor(Math.random() * (chineseEnd - chineseStart + 1) + chineseStart)
    )
  } else {
    // 20% 数字和特殊符号
    return numbers.charAt(Math.floor(Math.random() * numbers.length))
  }
}

const createColumns = () => {
  const canvasEl = canvas.value
  if (!canvasEl) return
  
  const columnCount = Math.floor(canvasEl.width / fontSize)
  columns = []
  
  for (let i = 0; i < columnCount; i++) {
    const column: Column = {
      x: i * fontSize,
      chars: [],
      y: Math.random() * -1000, // 从屏幕外开始
      speed: Math.random() * 5 + 3,
      opacity: Math.random() * 0.3 + 0.1,
      fadeSpeed: Math.random() * 0.04 + 0.01
    }
    
    // 创建不同长度的字符串
    const length = Math.floor(Math.random() * 30) + 5
    for (let j = 0; j < length; j++) {
      column.chars.push(characters())
    }
    
    columns.push(column)
  }
}

const drawMatrix = () => {
  const canvasEl = canvas.value
  if (!canvasEl || !ctx) return
  
  // 降低黑色背景的不透明度，避免累积覆盖页面内容
  // 从0.05降低到0.03
  ctx.fillStyle = 'rgba(0, 0, 0, 0.03)'
  ctx.fillRect(0, 0, canvasEl.width, canvasEl.height)
  
  // 设置字体
  ctx.font = `${fontSize}px ${fontFamily}`
  
  columns.forEach(column => {
    // 计算当前可见区域内的字符数量
    const visibleCharCount = Math.min(
      Math.ceil(column.chars.length), 
      Math.ceil((canvasEl.height - column.y) / fontSize) + 1
    )
    
    // 绘制每一列的字符
    for (let i = 0; i < visibleCharCount; i++) {
      const y = column.y + i * fontSize
      
      // 如果超出屏幕底部，则跳过
      if (y < 0 || y > canvasEl.height) continue
      
      const charIndex = i % column.chars.length
      const char = column.chars[charIndex]
      
      // 第一个字符使用亮绿色，后面的字符逐渐变暗
      const alpha = column.opacity - (i * column.fadeSpeed)
      if (alpha <= 0) continue
      
      if (i === 0) {
        // 增加头部字符亮度，使其更明显
        if (ctx) ctx.fillStyle = `rgba(180, 255, 220, ${alpha + 0.2})`
      } else {
        // 增加尾部字符亮度，但保持较低透明度
        if (ctx) ctx.fillStyle = `rgba(70, 220, 120, ${alpha * 0.8})`
      }
      
      if (ctx) {
        ctx.fillText(char, column.x, y)
      }
      
      // 随机更改字符
      if (Math.random() < 0.01) {
        column.chars[charIndex] = characters()
      }
    }
    
    // 移动列
    column.y += column.speed
    
    // 如果列已经完全离开屏幕底部，重置到顶部
    if (column.y > canvasEl.height + column.chars.length * fontSize) {
      column.y = Math.random() * -500 - 200
      column.speed = Math.random() * 5 + 3
      column.opacity = Math.random() * 0.3 + 0.1
      column.fadeSpeed = Math.random() * 0.04 + 0.01
    }
  })
  
  animationFrameId = requestAnimationFrame(drawMatrix)
}

const handleResize = () => {
  const canvasEl = canvas.value
  if (!canvasEl) return
  canvasEl.width = window.innerWidth
  canvasEl.height = window.innerHeight
  createColumns()
}

// 启动动画
const startAnimation = () => {
  if (!animationFrameId) {
    // 动画启动时先清除画布，避免叠加效果
    clearCanvas()
    drawMatrix()
  }
}

// 停止动画
const stopAnimation = () => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = 0
  }
}

// 清除画布
const clearCanvas = () => {
  const canvasEl = canvas.value
  if (!canvasEl || !ctx) return
  ctx.clearRect(0, 0, canvasEl.width, canvasEl.height)
}

// 监听背景特效开关状态
watchEffect(() => {
  if (backgroundEffectStore.enabled && backgroundEffectStore.effectType === 'matrix') {
    startAnimation()
  } else {
    stopAnimation()
    clearCanvas()
  }
})

onMounted(() => {
  const canvasEl = canvas.value
  if (!canvasEl) return
  
  ctx = canvasEl.getContext('2d')
  if (!ctx) return

  handleResize()
  window.addEventListener('resize', handleResize)
  
  // 只有在矩阵特效启用且当前是矩阵特效时才启动动画
  if (backgroundEffectStore.enabled && backgroundEffectStore.effectType === 'matrix') {
    startAnimation()
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
  }
})
</script>

<template>
  <canvas
    ref="canvas"
    class="fixed inset-0 pointer-events-none z-[-1]"
    :class="{ 'opacity-0': !backgroundEffectStore.enabled || backgroundEffectStore.effectType !== 'matrix' }"
  />
</template>
