<script setup lang="ts">
import { Icon } from '@iconify/vue'

defineProps<{
  message: string
  onRetry?: () => void
}>()
</script>

<template>
  <div class="text-center flex flex-col items-center gap-4 p-6 bg-red-500/20 backdrop-blur-lg rounded-lg">
    <Icon icon="mdi:alert" class="w-12 h-12 text-red-400" />
    <p class="text-red-200">{{ message }}</p>
    <button 
      v-if="onRetry"
      @click="onRetry"
      class="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors duration-300 text-white"
    >
      Retry
    </button>
  </div>
</template>