<script setup lang="ts">
import { onMounted, onUnmounted, ref, watchEffect } from 'vue'
import { useBackgroundEffectStore } from '../../stores/backgroundEffect'

interface Particle {
  x: number
  y: number
  size: number
  baseSize: number
  speedX: number
  speedY: number
  opacity: number
  color: string
  pulse: number
  pulseSpeed: number
  lifespan: number
  maxLife: number
}

const canvas = ref<HTMLCanvasElement | null>(null)
let ctx: CanvasRenderingContext2D | null = null
let animationFrameId = 0
let particles: Particle[] = []
let mouse = { x: 0, y: 0 }
let hue = 0 // 用于颜色变换

// 使用背景特效状态管理
const backgroundEffectStore = useBackgroundEffectStore()

// 配置选项
const config = {
  colorMode: 'gradient', // 'white', 'gradient', 'random'
  enablePulse: true,
  connectionDistance: 120,
  particleDensity: 15000, // 数值越小，粒子越多
  particleLifespan: true, // 是否启用粒子生命周期
  maxParticleLife: 300,
  minParticleLife: 100,
  hueSpeed: 0.5 // 色调变换速度
}

const getParticleColor = (): string => {
  if (config.colorMode === 'white') {
    return 'rgba(255, 255, 255, 1)'
  } else if (config.colorMode === 'gradient') {
    return `hsla(${hue}, 100%, 60%, 1)`
  } else {
    // random colors
    return `hsla(${Math.random() * 360}, 100%, 60%, 1)`
  }
}

const createParticle = (x: number, y: number): Particle => {
  const baseSize = Math.random() * 3 + 0.5
  const lifespan = config.particleLifespan ? 
    Math.random() * (config.maxParticleLife - config.minParticleLife) + config.minParticleLife : Infinity
  
  return {
    x,
    y,
    baseSize,
    size: baseSize,
    speedX: Math.random() * 1 - 0.5,
    speedY: Math.random() * 1 - 0.5,
    opacity: Math.random() * 0.5 + 0.3,
    color: getParticleColor(),
    pulse: 0,
    pulseSpeed: Math.random() * 0.1 + 0.01,
    lifespan,
    maxLife: lifespan
  }
}

const initParticles = () => {
  const canvasEl = canvas.value
  if (!canvasEl) return

  const numberOfParticles = Math.floor((window.innerWidth * window.innerHeight) / config.particleDensity)
  particles = Array.from({ length: numberOfParticles }, () => 
    createParticle(
      Math.random() * canvasEl.width,
      Math.random() * canvasEl.height
    )
  )
}

const drawParticle = (particle: Particle, context: CanvasRenderingContext2D, canvasEl: HTMLCanvasElement) => {
  // 更新粒子生命周期
  if (config.particleLifespan) {
    particle.lifespan--
    if (particle.lifespan <= 0) {
      // 重置粒子
      const newParticle = createParticle(Math.random() * canvasEl.width, Math.random() * canvasEl.height)
      Object.assign(particle, newParticle)
      return
    }
    // 接近生命周期末尾时逐渐变透明
    const fadeThreshold = 20
    if (particle.lifespan < fadeThreshold) {
      particle.opacity = (particle.lifespan / fadeThreshold) * (Math.random() * 0.5 + 0.3)
    }
  }

  // 鼠标交互
  const dx = mouse.x - particle.x
  const dy = mouse.y - particle.y
  const distance = Math.sqrt(dx * dx + dy * dy)
  
  if (distance < 100) {
    const angle = Math.atan2(dy, dx)
    const force = (100 - distance) / 100
    particle.speedX -= Math.cos(angle) * force * 0.3
    particle.speedY -= Math.sin(angle) * force * 0.3
  }
  
  // 限制速度
  particle.speedX = Math.min(Math.max(particle.speedX, -2.5), 2.5)
  particle.speedY = Math.min(Math.max(particle.speedY, -2.5), 2.5)
  
  // 更新位置
  particle.x += particle.speedX
  particle.y += particle.speedY
  
  // 自然阻尼运动
  particle.speedX *= 0.98
  particle.speedY *= 0.98
  
  // 添加微小随机移动
  particle.speedX += (Math.random() - 0.5) * 0.05
  particle.speedY += (Math.random() - 0.5) * 0.05
  
  // 边界弹回
  if (particle.x < 0) {
    particle.x = 0
    particle.speedX *= -1
  }
  if (particle.x > canvasEl.width) {
    particle.x = canvasEl.width
    particle.speedX *= -1
  }
  if (particle.y < 0) {
    particle.y = 0
    particle.speedY *= -1
  }
  if (particle.y > canvasEl.height) {
    particle.y = canvasEl.height
    particle.speedY *= -1
  }
  
  // 大小脉动效果
  if (config.enablePulse) {
    particle.pulse += particle.pulseSpeed
    particle.size = particle.baseSize + Math.sin(particle.pulse) * particle.baseSize * 0.5
  }
  
  // 绘制粒子
  context.beginPath()
  context.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
  
  // 设置粒子颜色
  if (config.colorMode === 'white') {
    context.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`
  } else {
    const color = particle.color.replace('1)', `${particle.opacity})`)
    context.fillStyle = color
  }
  
  context.fill()
}

const drawConnections = (particle: Particle, context: CanvasRenderingContext2D) => {
  particles.forEach(otherParticle => {
    if (particle === otherParticle) return
    
    const dx = particle.x - otherParticle.x
    const dy = particle.y - otherParticle.y
    const distance = Math.sqrt(dx * dx + dy * dy)
    
    if (distance < config.connectionDistance) {
      const opacity = 0.2 * (1 - distance / config.connectionDistance)
      context.beginPath()
      
      if (config.colorMode === 'white') {
        context.strokeStyle = `rgba(255, 255, 255, ${opacity})`
      } else {
        const gradient = context.createLinearGradient(
          particle.x, particle.y, otherParticle.x, otherParticle.y
        )
        gradient.addColorStop(0, particle.color.replace('1)', `${opacity})`))
        gradient.addColorStop(1, otherParticle.color.replace('1)', `${opacity})`))
        context.strokeStyle = gradient
      }
      
      context.lineWidth = Math.min(particle.size, otherParticle.size) * 0.3
      context.moveTo(particle.x, particle.y)
      context.lineTo(otherParticle.x, otherParticle.y)
      context.stroke()
    }
  })
}

const drawParticles = () => {
  const canvasEl = canvas.value
  if (!canvasEl || !ctx) return
  
  ctx.clearRect(0, 0, canvasEl.width, canvasEl.height)
  
  // 更新色相
  hue = (hue + config.hueSpeed) % 360
  
  particles.forEach(particle => {
    if (ctx) {
      drawParticle(particle, ctx, canvasEl)
      drawConnections(particle, ctx)
    }
  })
  
  animationFrameId = requestAnimationFrame(drawParticles)
}

const handleMouseMove = (e: MouseEvent) => {
  const canvasEl = canvas.value
  if (!canvasEl) return
  const rect = canvasEl.getBoundingClientRect()
  mouse.x = e.clientX - rect.left
  mouse.y = e.clientY - rect.top
}

const handleResize = () => {
  const canvasEl = canvas.value
  if (!canvasEl) return
  canvasEl.width = window.innerWidth
  canvasEl.height = window.innerHeight
  initParticles()
}

// 启动粒子动画
const startAnimation = () => {
  if (!animationFrameId) {
    drawParticles()
  }
}

// 停止粒子动画
const stopAnimation = () => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = 0
  }
}

// 清除画布
const clearCanvas = () => {
  const canvasEl = canvas.value
  if (!canvasEl || !ctx) return
  ctx.clearRect(0, 0, canvasEl.width, canvasEl.height)
}

// 监听背景特效开关状态
watchEffect(() => {
  if (backgroundEffectStore.enabled && backgroundEffectStore.effectType === 'particles') {
    startAnimation()
  } else {
    stopAnimation()
    clearCanvas()
  }
})

onMounted(() => {
  const canvasEl = canvas.value
  if (!canvasEl) return
  
  ctx = canvasEl.getContext('2d')
  if (!ctx) return

  handleResize()
  window.addEventListener('resize', handleResize)
  window.addEventListener('mousemove', handleMouseMove)
  
  // 只有在粒子特效启用且当前是粒子特效时才启动动画
  if (backgroundEffectStore.enabled && backgroundEffectStore.effectType === 'particles') {
    drawParticles()
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('mousemove', handleMouseMove)
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
  }
})
</script>

<template>
  <canvas
    ref="canvas"
    class="fixed inset-0 pointer-events-none z-0"
    :class="{ 'opacity-0': !backgroundEffectStore.enabled || backgroundEffectStore.effectType !== 'particles' }"
  />
</template>