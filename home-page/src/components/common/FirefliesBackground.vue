// 萤火虫特效：
// - 模拟光点随机游走的路径
// - 光点会闪烁并有尾迹
// - 黄色/橙色的色调
// - 效果类似于夏夜的萤火虫

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watchEffect } from 'vue'
import { useBackgroundEffectStore } from '../../stores/backgroundEffect'

const canvas = ref<HTMLCanvasElement | null>(null)
let ctx: CanvasRenderingContext2D | null = null
let animationFrameId = 0
let fireflies: Firefly[] = []

const backgroundEffectStore = useBackgroundEffectStore()

interface Firefly {
  x: number
  y: number
  size: number
  blink: number
  blinkSpeed: number
  maxBrightness: number
  hue: number
  speed: number
  direction: number
  wanderIntensity: number
  wanderStep: number
  trailLength: number
  trail: {x: number, y: number, alpha: number}[]
}

// 配置选项
const config = {
  fireflyCount: 60,
  baseHue: 55, // 黄色火光
  hueVariation: 20,
  trailSize: 8 // 最大尾迹长度
}

const createFirefly = (width: number, height: number): Firefly => {
  const size = Math.random() * 3 + 1
  const trailLength = Math.floor(Math.random() * config.trailSize) + 1
  
  return {
    x: Math.random() * width,
    y: Math.random() * height,
    size,
    blink: Math.random() * Math.PI * 2, // 随机闪烁相位
    blinkSpeed: Math.random() * 0.05 + 0.01,
    maxBrightness: Math.random() * 0.4 + 0.6,
    hue: config.baseHue + (Math.random() * config.hueVariation * 2 - config.hueVariation),
    speed: Math.random() * 0.5 + 0.2,
    direction: Math.random() * Math.PI * 2, // 随机方向 (0-2π)
    wanderIntensity: Math.random() * 0.1 + 0.05, // 随机游走强度
    wanderStep: 0,
    trailLength,
    trail: [] // 用于存储尾迹点
  }
}

const initFireflies = () => {
  const canvasEl = canvas.value
  if (!canvasEl) return
  
  fireflies = Array.from(
    { length: config.fireflyCount }, 
    () => createFirefly(canvasEl.width, canvasEl.height)
  )
}

const drawFireflies = () => {
  const canvasEl = canvas.value
  if (!canvasEl || !ctx) return
  
  ctx.clearRect(0, 0, canvasEl.width, canvasEl.height)
  
  fireflies.forEach(firefly => {
    // 更新闪烁
    firefly.blink += firefly.blinkSpeed
    const brightness = Math.sin(firefly.blink) * 0.5 + 0.5
    const finalBrightness = brightness * firefly.maxBrightness
    
    // 更新方向 - 随机漫步
    firefly.wanderStep += 0.05
    firefly.direction += Math.sin(firefly.wanderStep) * firefly.wanderIntensity
    
    // 更新位置
    const dx = Math.cos(firefly.direction) * firefly.speed
    const dy = Math.sin(firefly.direction) * firefly.speed
    firefly.x += dx
    firefly.y += dy
    
    // 添加新的尾迹点
    if (firefly.trail.length === firefly.trailLength) {
      firefly.trail.pop() // 移除最老的点
    }
    
    // 新点放在开头
    firefly.trail.unshift({
      x: firefly.x,
      y: firefly.y,
      alpha: finalBrightness
    })
    
    // 减少所有尾迹点的不透明度
    for (let i = 1; i < firefly.trail.length; i++) {
      firefly.trail[i].alpha *= 0.85
    }
    
    // 绘制尾迹
    if (firefly.trail.length > 1) {
      for (let i = 1; i < firefly.trail.length; i++) {
        const point = firefly.trail[i]
        const prevPoint = firefly.trail[i - 1]
        
        const size = firefly.size * (1 - i / firefly.trail.length)
        const alpha = point.alpha * 0.2
        
        // 绘制尾迹点
        if (ctx) {
          ctx.beginPath()
          ctx.arc(point.x, point.y, size, 0, Math.PI * 2)
          ctx.fillStyle = `hsla(${firefly.hue}, 100%, 70%, ${alpha})`
          ctx.fill()
          
          // 连接点线
          ctx.beginPath()
          ctx.moveTo(prevPoint.x, prevPoint.y)
          ctx.lineTo(point.x, point.y)
          ctx.strokeStyle = `hsla(${firefly.hue}, 100%, 70%, ${alpha * 0.5})`
          ctx.lineWidth = size * 0.8
          ctx.stroke()
        }
      }
    }
    
    // 绘制萤火虫主体
    if (ctx) {
      ctx.beginPath()
      
      // 创建径向渐变
      const gradient = ctx.createRadialGradient(
        firefly.x, firefly.y, 0,
        firefly.x, firefly.y, firefly.size * 2
      )
      
      gradient.addColorStop(0, `hsla(${firefly.hue}, 100%, 80%, ${finalBrightness})`)
      gradient.addColorStop(0.5, `hsla(${firefly.hue}, 100%, 60%, ${finalBrightness * 0.6})`)
      gradient.addColorStop(1, `hsla(${firefly.hue}, 100%, 50%, 0)`)
      
      ctx.fillStyle = gradient
      ctx.arc(firefly.x, firefly.y, firefly.size * 2, 0, Math.PI * 2)
      ctx.fill()
      
      // 添加明亮的中心
      ctx.beginPath()
      ctx.arc(firefly.x, firefly.y, firefly.size * 0.7, 0, Math.PI * 2)
      ctx.fillStyle = `rgba(255, 255, 255, ${finalBrightness * 0.7})`
      ctx.fill()
    }
    
    // 边界检查与环绕
    if (firefly.x < -firefly.size * 2) firefly.x = canvasEl.width + firefly.size * 2
    if (firefly.x > canvasEl.width + firefly.size * 2) firefly.x = -firefly.size * 2
    if (firefly.y < -firefly.size * 2) firefly.y = canvasEl.height + firefly.size * 2
    if (firefly.y > canvasEl.height + firefly.size * 2) firefly.y = -firefly.size * 2
  })
  
  animationFrameId = requestAnimationFrame(drawFireflies)
}

const handleResize = () => {
  const canvasEl = canvas.value
  if (!canvasEl) return
  canvasEl.width = window.innerWidth
  canvasEl.height = window.innerHeight
  initFireflies()
}

// 启动动画
const startAnimation = () => {
  if (!animationFrameId) {
    drawFireflies()
  }
}

// 停止动画
const stopAnimation = () => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = 0
  }
}

// 清除画布
const clearCanvas = () => {
  const canvasEl = canvas.value
  if (!canvasEl || !ctx) return
  ctx.clearRect(0, 0, canvasEl.width, canvasEl.height)
}

// 监听背景特效开关状态
watchEffect(() => {
  if (backgroundEffectStore.enabled && backgroundEffectStore.effectType === 'fireflies') {
    startAnimation()
  } else {
    stopAnimation()
    clearCanvas()
  }
})

onMounted(() => {
  const canvasEl = canvas.value
  if (!canvasEl) return
  
  ctx = canvasEl.getContext('2d')
  if (!ctx) return

  handleResize()
  window.addEventListener('resize', handleResize)
  
  if (backgroundEffectStore.enabled && backgroundEffectStore.effectType === 'fireflies') {
    startAnimation()
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
  }
})
</script>

<template>
  <canvas
    ref="canvas"
    class="fixed inset-0 pointer-events-none z-0"
    :class="{ 'opacity-0': !backgroundEffectStore.enabled || backgroundEffectStore.effectType !== 'fireflies' }"
  />
</template>
