<script setup lang="ts">
import { onMounted, onUnmounted, ref, watchEffect } from 'vue'
import { useBackgroundEffectStore } from '../../stores/backgroundEffect'

const canvas = ref<HTMLCanvasElement | null>(null)
let ctx: CanvasRenderingContext2D | null = null
let animationFrameId = 0
let bubbles: Bubble[] = []

const backgroundEffectStore = useBackgroundEffectStore()

interface Bubble {
  x: number
  y: number
  size: number
  speed: number
  opacity: number
  hue: number
  wobble: number
  wobbleSpeed: number
  wobbleSize: number
  innerBrightness: number
}

// 配置选项
const config = {
  bubbleCount: 50,
  minSize: 5,
  maxSize: 50,
  colorVariation: 40, // 色调变化范围
  baseHue: 200, // 基础色调 (蓝色)
  speedFactor: 0.8
}

const createBubble = (width: number): Bubble => {
  const size = Math.random() * (config.maxSize - config.minSize) + config.minSize
  return {
    x: Math.random() * width,
    y: window.innerHeight + size * 2, // 从屏幕底部下方开始
    size,
    speed: (Math.random() * 0.8 + 0.5) * config.speedFactor * (1 / (size / 15)), // 小气泡移动更快
    opacity: Math.random() * 0.4 + 0.2,
    hue: config.baseHue + (Math.random() * config.colorVariation * 2 - config.colorVariation),
    wobble: Math.random() * Math.PI * 2,
    wobbleSpeed: Math.random() * 0.03 + 0.01,
    wobbleSize: Math.random() * 2 + 1,
    innerBrightness: Math.random() * 0.3 + 0.6
  }
}

const initBubbles = () => {
  const canvasEl = canvas.value
  if (!canvasEl) return
  
  bubbles = Array.from({ length: config.bubbleCount }, () => createBubble(canvasEl.width))
}

const drawBubble = (bubble: Bubble) => {
  if (!ctx) return
  
  // 更新气泡位置
  bubble.y -= bubble.speed
  
  // 更新左右摆动
  bubble.wobble += bubble.wobbleSpeed
  const wobbleX = Math.sin(bubble.wobble) * bubble.wobbleSize
  
  // 如果气泡移出屏幕上方，重新放置到底部
  if (bubble.y < -bubble.size * 2) {
    bubble.y = window.innerHeight + bubble.size * 2
    bubble.x = Math.random() * (canvas.value?.width || window.innerWidth)
    bubble.hue = config.baseHue + (Math.random() * config.colorVariation * 2 - config.colorVariation)
    bubble.opacity = Math.random() * 0.4 + 0.2
  }
  
  const x = bubble.x + wobbleX
  const y = bubble.y
  const size = bubble.size
  
  // 绘制气泡
  ctx.beginPath()
  ctx.arc(x, y, size, 0, Math.PI * 2)
  
  // 创建径向渐变
  const gradient = ctx.createRadialGradient(
    x - size * 0.3, y - size * 0.3, size * 0.1, // 内圆
    x, y, size // 外圆
  )
  
  // 设置渐变颜色
  gradient.addColorStop(0, `hsla(${bubble.hue}, 100%, ${bubble.innerBrightness * 100}%, ${bubble.opacity * 1.5})`)
  gradient.addColorStop(0.6, `hsla(${bubble.hue}, 70%, 60%, ${bubble.opacity * 0.6})`)
  gradient.addColorStop(1, `hsla(${bubble.hue}, 60%, 50%, 0)`)
  
  ctx.fillStyle = gradient
  ctx.fill()
  
  // 绘制气泡高光
  ctx.beginPath()
  ctx.arc(
    x - size * 0.3,
    y - size * 0.3,
    size * 0.2,
    0,
    Math.PI * 2
  )
  ctx.fillStyle = `rgba(255, 255, 255, ${bubble.opacity * 0.6})`
  ctx.fill()
}

const drawBubbles = () => {
  const canvasEl = canvas.value
  if (!canvasEl || !ctx) return
  
  ctx.clearRect(0, 0, canvasEl.width, canvasEl.height)
  
  bubbles.forEach(bubble => {
    drawBubble(bubble)
  })
  
  animationFrameId = requestAnimationFrame(drawBubbles)
}

const handleResize = () => {
  const canvasEl = canvas.value
  if (!canvasEl) return
  canvasEl.width = window.innerWidth
  canvasEl.height = window.innerHeight
  initBubbles()
}

// 启动动画
const startAnimation = () => {
  if (!animationFrameId) {
    drawBubbles()
  }
}

// 停止动画
const stopAnimation = () => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = 0
  }
}

// 清除画布
const clearCanvas = () => {
  const canvasEl = canvas.value
  if (!canvasEl || !ctx) return
  ctx.clearRect(0, 0, canvasEl.width, canvasEl.height)
}

// 监听背景特效开关状态
watchEffect(() => {
  if (backgroundEffectStore.enabled && backgroundEffectStore.effectType === 'bubbles') {
    startAnimation()
  } else {
    stopAnimation()
    clearCanvas()
  }
})

onMounted(() => {
  const canvasEl = canvas.value
  if (!canvasEl) return
  
  ctx = canvasEl.getContext('2d')
  if (!ctx) return

  handleResize()
  window.addEventListener('resize', handleResize)
  
  if (backgroundEffectStore.enabled && backgroundEffectStore.effectType === 'bubbles') {
    startAnimation()
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
  }
})
</script>

<template>
  <canvas
    ref="canvas"
    class="fixed inset-0 pointer-events-none z-0"
    :class="{ 'opacity-0': !backgroundEffectStore.enabled || backgroundEffectStore.effectType !== 'bubbles' }"
  />
</template>
