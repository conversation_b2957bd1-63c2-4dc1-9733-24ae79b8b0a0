<script setup lang="ts">
import { ref, computed } from 'vue'
import { Icon } from '@iconify/vue'
import { useThemeStore } from '../stores/theme'
import { useLangStore } from '../stores/lang'
import { useI18n } from 'vue-i18n'
import { useBackgroundEffectStore } from '../stores/backgroundEffect'
import type { BackgroundEffectType } from '../stores/backgroundEffect'

// 导入背景特效状态管理
const backgroundEffectStore = useBackgroundEffectStore()

interface CustomColor {
  from: string
  via: string
  to: string
  labelZh: string
  labelEn: string
  [key: string]: string
}

const colors = [
  { from: 'from-blue-500/40', via: 'via-pink-400/40', to: 'to-purple-500/40', labelZh: '默认', labelEn: 'Default' },
  { from: 'from-rose-500/40', via: 'via-purple-400/40', to: 'to-blue-500/40', labelZh: '晚霞', labelEn: 'Sunset' },
  { from: 'from-green-500/40', via: 'via-teal-400/40', to: 'to-blue-500/40', labelZh: '海洋', labelEn: 'Ocean' },
  { from: 'from-amber-500/40', via: 'via-rose-400/40', to: 'to-purple-500/40', labelZh: '黎明', labelEn: 'Dawn' },
  { from: 'from-indigo-500/40', via: 'via-purple-400/40', to: 'to-pink-500/40', labelZh: '梦幻', labelEn: 'Dream' },
  { from: 'from-emerald-500/40', via: 'via-green-400/40', to: 'to-teal-500/40', labelZh: '森林', labelEn: 'Forest' },
  { from: 'from-violet-500/40', via: 'via-fuchsia-400/40', to: 'to-pink-500/40', labelZh: '极光', labelEn: 'Aurora' },
  { from: 'from-yellow-500/40', via: 'via-orange-400/40', to: 'to-red-500/40', labelZh: '阳光', labelEn: 'Sunshine' },
  { from: 'from-cyan-500/40', via: 'via-sky-400/40', to: 'to-blue-500/40', labelZh: '清晨', labelEn: 'Morning' },
  { from: 'from-pink-500/40', via: 'via-rose-400/40', to: 'to-red-500/40', labelZh: '樱花', labelEn: 'Sakura' },
  { from: 'from-purple-500/40', via: 'via-indigo-400/40', to: 'to-blue-500/40', labelZh: '星空', labelEn: 'Starry' },
  { from: 'from-lime-500/40', via: 'via-green-400/40', to: 'to-emerald-500/40', labelZh: '春意', labelEn: 'Spring' },
  { from: 'from-orange-500/40', via: 'via-amber-400/40', to: 'to-yellow-500/40', labelZh: '秋色', labelEn: 'Autumn' },
  { from: 'from-teal-500/40', via: 'via-cyan-400/40', to: 'to-sky-500/40', labelZh: '碧波', labelEn: 'Waves' },
  { from: 'from-fuchsia-500/40', via: 'via-pink-400/40', to: 'to-rose-500/40', labelZh: '霓虹', labelEn: 'Neon' }
]

const isOpen = ref(false)
const activeTab = ref('preset') // 'preset', 'custom' 或 'effects'
const customColor = ref<CustomColor>({
  from: '#3B82F6',
  via: '#F472B6',
  to: '#9333EA',
  labelZh: '自定义',
  labelEn: 'Custom'
})

const themeStore = useThemeStore()
const langStore = useLangStore()
const { locale } = useI18n()

const selectGradient = (color: typeof colors[0]) => {
  themeStore.saveTheme({
    ...color,
    label: locale.value === 'zh' ? color.labelZh : color.labelEn
  })
}

// 修改应用自定义渐变的函数，使用内联样式而不是 Tailwind 类
const applyCustomGradient = () => {
  const theme = {
    // 储存原始颜色值，而不是转换为 tailwind 类
    from: customColor.value.from,
    via: customColor.value.via,
    to: customColor.value.to,
    labelZh: '自定义',
    labelEn: 'Custom',
    label: locale.value === 'zh' ? '自定义' : 'Custom',
    // 标记为自定义主题
    isCustom: true
  }
  themeStore.saveTheme(theme)
}

const toggleLang = () => {
  const newLang = locale.value === 'zh' ? 'en' : 'zh'
  locale.value = newLang
  langStore.setLang(newLang)
}

const getColorLabel = (color: typeof colors[0]) => {
  return locale.value === 'zh' ? color.labelZh : color.labelEn
}

// 预览渐变
const previewGradient = computed(() => {
  return {
    background: `linear-gradient(45deg, ${customColor.value.from}, ${customColor.value.via}, ${customColor.value.to})`
  }
})

const effectTypes = [
  { type: 'none', iconZh: '无特效', iconEn: 'None', icon: 'mdi:close-circle-outline' },
  { type: 'particles', iconZh: '粒子', iconEn: 'Particles', icon: 'mdi:chart-bubble' },
  { type: 'waves', iconZh: '波浪', iconEn: 'Waves', icon: 'mdi:wave' },
  { type: 'matrix', iconZh: '矩阵', iconEn: 'Matrix', icon: 'mdi:matrix' },
  { type: 'bubbles', iconZh: '气泡', iconEn: 'Bubbles', icon: 'mdi:water' },
  { type: 'fireflies', iconZh: '萤火虫', iconEn: 'Fireflies', icon: 'mdi:weather-night' }
] as const

const setEffectType = (type: BackgroundEffectType) => {
  backgroundEffectStore.setEffectType(type)
}
</script>

<template>
  <div class="fixed bottom-8 right-8 z-50 flex flex-col gap-4">
    <!-- 语言切换按钮 -->
    <button
      @click="toggleLang"
      class="w-12 h-12 rounded-full bg-white/10 backdrop-blur-lg shadow-lg flex items-center justify-center hover:bg-white/20 transition-all duration-300"
    >
      <span class="text-white font-semibold">{{ locale === 'zh' ? 'EN' : 'ZH' }}</span>
    </button>

    <!-- 主题按钮 -->
    <button
      @click="isOpen = !isOpen"
      class="w-12 h-12 rounded-full bg-white/10 backdrop-blur-lg shadow-lg flex items-center justify-center hover:bg-white/20 transition-all duration-300"
    >
      <Icon icon="mdi:palette-outline" class="w-6 h-6 text-white" />
    </button>

    <!-- 主题选择面板 -->
    <div
      v-if="isOpen"
      class="absolute bottom-16 right-0 bg-white/10 backdrop-blur-lg rounded-lg shadow-xl p-6 w-96 border border-white/20"
    >
      <!-- 标签切换 -->
      <div class="flex gap-2 mb-6">
        <button
          v-for="tab in ['preset', 'custom', 'effects']"
          :key="tab"
          @click="activeTab = tab"
          class="flex-1 py-2 px-4 rounded-lg transition-all duration-200 font-medium"
          :class="activeTab === tab ? 'bg-white/20' : 'hover:bg-white/10'"
        >
          <span class="text-white">{{ locale === 'zh' ? (tab === 'preset' ? '预设' : tab === 'custom' ? '自定义' : '特效') : (tab === 'preset' ? 'Preset' : tab === 'custom' ? 'Custom' : 'Effects') }}</span>
        </button>
      </div>

      <!-- 面板内容区域 - 添加统一的高度和样式 -->
      <div class="panel-content-container h-[230px] overflow-hidden">
        <!-- 预设颜色面板 -->
        <div v-if="activeTab === 'preset'" class="h-full overflow-y-auto custom-scrollbar pr-2">
          <div class="grid grid-cols-2 gap-3">
            <button
              v-for="color in colors"
              :key="color.labelEn"
              @click="selectGradient(color)"
              class="group p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-all duration-300 border border-white/10 hover:border-white/20"
            >
              <div
                class="w-full h-16 rounded-lg bg-gradient-to-r mb-3 transform group-hover:scale-105 transition-all duration-300 shadow-lg"
                :class="[color.from, color.via, color.to]"
              ></div>
              <span class="text-sm text-white font-medium">{{ getColorLabel(color) }}</span>
            </button>
          </div>
        </div>

        <!-- 自定义颜色面板 -->
        <div v-else-if="activeTab === 'custom'" class="h-full flex flex-col justify-between">
          <div class="space-y-6">
            <!-- 预览区域 -->
            <div class="w-full h-16 rounded-lg shadow-lg" :style="previewGradient"></div>
            
            <!-- 颜色选择器 -->
            <div class="grid grid-cols-3 gap-2">
              <div 
                v-for="(key, index) in ['from', 'via', 'to']" 
                :key="key" 
                class="flex flex-col"
              >
                <label class="text-xs text-white font-medium mb-1 flex justify-between">
                  <span>{{ locale === 'zh' ? 
                    (index === 0 ? '起始' : index === 1 ? '过渡' : '结束') : 
                    (index === 0 ? 'Start' : index === 1 ? 'Middle' : 'End') }}</span>
                  <span class="text-[10px] text-gray-400 truncate ml-1" :title="customColor[key]">
                    {{ customColor[key].slice(0, 7) }}
                  </span>
                </label>
                <input
                  type="color"
                  v-model="customColor[key]"
                  class="w-full h-8 rounded-md bg-white/5 border border-white/20 cursor-pointer hover:border-white/40 transition-all duration-300"
                />
              </div>
            </div>
          </div>
          
          <!-- 应用按钮 -->
          <button
            @click="applyCustomGradient"
            class="w-full py-2.5 px-4 mt-6 rounded-lg bg-white/10 hover:bg-white/20 transition-all duration-300 text-white font-medium flex items-center justify-center gap-2"
          >
            <Icon icon="mdi:check" class="w-5 h-5" />
            {{ locale === 'zh' ? '应用' : 'Apply' }}
          </button>
        </div>

        <!-- 特效面板 -->
        <div v-else-if="activeTab === 'effects'" class="h-full overflow-y-auto custom-scrollbar pr-2">
          <h3 class="text-white text-lg font-medium mb-4">{{ locale === 'zh' ? '背景特效' : 'Background Effects' }}</h3>
          
          <!-- 特效类型选择 -->
          <div class="grid grid-cols-2 gap-3">
            <button
              v-for="effect in effectTypes"
              :key="effect.type"
              @click="setEffectType(effect.type)"
              class="group p-4 rounded-lg transition-all duration-300 border flex flex-col items-center gap-3"
              :class="backgroundEffectStore.effectType === effect.type ? 
                    'bg-white/20 border-white/30' : 
                    'bg-white/5 border-white/10 hover:bg-white/10 hover:border-white/20'"
            >
              <Icon :icon="effect.icon" class="w-8 h-8 text-white" />
              <span class="text-sm text-white font-medium">
                {{ locale === 'zh' ? effect.iconZh : effect.iconEn }}
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.panel-content-container {
  /* 添加过渡效果使切换面板更平滑 */
  transition: height 0.3s ease;
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-image: var(--scrollbar-gradient);
  border-radius: 3px;
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-image: linear-gradient(45deg, rgba(219, 234, 254, 0.8), rgba(255, 255, 255, 0.8));
}

input[type="color"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: 0;
}

input[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}

input[type="color"]::-webkit-color-swatch {
  border: none;
  border-radius: 0.5rem;
}
</style>