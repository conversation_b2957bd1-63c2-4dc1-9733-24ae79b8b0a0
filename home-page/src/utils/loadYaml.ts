import yaml from 'js-yaml'

export async function loadYamlData<T>(path: string): Promise<T> {
  try {
    const normalizedPath = `/data/${path.replace(/^[\/]*(data\/)?/, '')}`
    const response = await fetch(normalizedPath)
    if (!response.ok) {
      throw new Error(`Failed to load YAML file: ${normalizedPath}`)
    }
    const yamlText = await response.text()
    return yaml.load(yamlText) as T
  } catch (error) {
    console.error(`Error loading YAML data from ${path}:`, error)
    throw error
  }
}