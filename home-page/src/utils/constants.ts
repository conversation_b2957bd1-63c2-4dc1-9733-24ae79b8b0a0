import type { GradientTheme } from '../types/theme'

export const STORAGE_KEYS = {
  THEME: 'theme',
  LANG: 'lang',
  PATH: 'path',
  PARTICLES: 'particles_enabled',
  EFFECT_TYPE: 'effect_type',
  EFFECT_ENABLED: 'effect_enabled'
} as const

export const DEFAULT_GRADIENT: GradientTheme = {
  from: 'from-blue-500/40',
  via: 'via-pink-400/40',
  to: 'to-purple-500/40',
  label: '默认',
  labelZh: '默认',
  labelEn: 'Default'
}

export const ANIMATION_DURATION = {
  FAST: 300,
  NORMAL: 500,
  SLOW: 800
} as const