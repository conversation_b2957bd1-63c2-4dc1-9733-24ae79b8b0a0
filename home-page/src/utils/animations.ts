import { gsap } from 'gsap'
import type { Ref } from 'vue'

export const fadeInUp = (el: Element, done: () => void) => {
  gsap.fromTo(
    el,
    {
      opacity: 0,
      y: 20
    },
    {
      opacity: 1,
      y: 0,
      duration: 0.5,
      onComplete: done
    }
  )
}

export const useParallax = (target: Ref<HTMLElement | null>) => {
  const handleScroll = () => {
    if (!target.value) return
    const scrolled = window.scrollY
    const rate = scrolled * 0.3
    gsap.to(target.value, {
      y: rate,
      duration: 0.5,
      ease: 'power1.out'
    })
  }

  return { handleScroll }
}