{"name": "vite-vue-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.5.10", "@vueuse/core": "^10.9.0", "gsap": "^3.12.5", "@iconify/vue": "^4.1.1", "vue-router": "^4.3.0", "pinia": "^2.1.7", "vue-i18n": "^9.9.1", "@vueuse/motion": "^2.1.0", "nprogress": "^0.2.0", "js-yaml": "^4.1.0", "md-editor-v3": "^4.12.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "typescript": "^5.5.3", "vite": "^5.4.8", "vue-tsc": "^2.1.6", "autoprefixer": "^10.4.18", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "@types/nprogress": "^0.2.3", "@types/js-yaml": "^4.0.9", "@types/node": "^20.11.24"}}