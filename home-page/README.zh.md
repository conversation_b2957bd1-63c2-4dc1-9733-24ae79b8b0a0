# 个人作品集网站

一个使用 Vue 3、TypeScript 和 Tailwind CSS 构建的现代响应式作品集网站。具有优美的渐变主题系统、国际化支持和流畅的动画效果。

![作品集预览](https://image.xwysyy.cn/img-icon/%E5%A4%B4%E5%83%8F.jpg)

## 特性

- 🎨 **动态主题系统**
  - 精美的渐变主题
  - 实时主题切换
  - 主题持久化保存

- 🌐 **国际化**
  - 中英文支持
  - 基于 YAML 的翻译系统
  - 自动语言检测

- 📱 **响应式设计**
  - 移动优先设计
  - 流畅动画效果
  - 全设备优化

- 🛠 **技术栈**
  - Vue 3 组合式 API
  - TypeScript 类型安全
  - Tailwind CSS 样式
  - Vite 快速开发
  - YAML 内容管理

## 项目结构

```
src/
├── components/      # 可复用的 Vue 组件
├── data/           # YAML 内容文件
├── i18n/           # 国际化配置
├── stores/         # Pinia 状态管理
├── views/          # 页面组件
└── styles/         # 全局样式
```

## 快速开始

1. 克隆仓库：
   ```bash
   git clone https://github.com/yourusername/portfolio.git
   ```

2. 安装依赖：
   ```bash
   npm install
   ```

3. 启动开发服务器：
   ```bash
   npm run dev
   ```

4. 构建生产版本：
   ```bash
   npm run build
   ```

## 内容管理

所有内容通过 `src/data` 目录下的 YAML 文件管理：

- `basic.yaml`: 基本信息和个人简介
- `skills.yaml`: 技能和技术栈
- `projects.yaml`: 项目展示
- `academic.yaml`: 学术成果
- `contact.yaml`: 联系方式
- `i18n.yaml`: 翻译文本

## 自定义

1. **主题系统**
   - 在 `ColorPicker.vue` 中添加新主题
   - 自定义渐变颜色
   - 修改过渡效果

2. **内容**
   - 更新 `src/data` 中的 YAML 文件
   - 根据需要添加新板块
   - 修改现有组件

3. **样式**
   - `tailwind.config.js` 中的 Tailwind 配置
   - `src/style.css` 中的全局样式
   - 组件特定样式

## 许可

MIT 许可证 - 欢迎将此项目用于您自己的作品集！

## 联系方式

- 邮箱：<EMAIL>
- GitHub：[github.com/xwysyy](https://github.com/xwysyy)