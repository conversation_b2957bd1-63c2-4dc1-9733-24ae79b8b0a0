- title: 基于深度学习的代码智能补全系统研究
  titleEn: Research on Deep Learning-Based Code Intelligent Completion System
  authors: [xwysyy]
  conference: International Conference on Software Engineering (ICSE 2024)
  abstract: |
    本研究提出了一种新的深度学习模型，用于提高代码补全的准确性和效率。通过分析大规模代码库，我们的模型能够更好地理解程序上下文和编程模式。

    主要创新点：
    1. 提出了新的注意力机制
    2. 改进了上下文编码方法
    3. 引入了代码语义分析

    模型性能评估：
    - 准确率提升：$\Delta P = 15\%$
    - 召回率提升：$\Delta R = 12\%$
    - F1分数：$F_1 = \frac{2 \times P \times R}{P + R}$

    实验结果表明，我们的方法在多个基准测试中都取得了显著的性能提升。
  abstractEn: |
    This research proposes a new deep learning model to improve the accuracy and efficiency of code completion. Through analyzing large-scale code repositories, our model can better understand program context and programming patterns.

    Key innovations:
    1. Proposed new attention mechanism
    2. Improved context encoding method
    3. Introduced code semantic analysis

    Model Performance Evaluation:
    - Accuracy Improvement: $\Delta P = 15\%$
    - Recall Improvement: $\Delta R = 12\%$
    - F1 Score: $F_1 = \frac{2 \times P \times R}{P + R}$

    Experimental results show that our method achieved significant performance improvements across multiple benchmarks.
  keywords: [深度学习, 代码补全, 软件工程]
  keywordsEn: [Deep Learning, Code Completion, Software Engineering]
  year: 2024