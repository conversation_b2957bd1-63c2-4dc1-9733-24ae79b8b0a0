import requests
import re
from http.server import BaseHTTPRequestHandler
import json
from urllib.parse import parse_qs, urlparse

def get_github_contributions(username):
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }

    url = f"https://github.com/{username}"
    response = requests.get(url, headers=headers)

    date_pattern = re.compile(r'data-date="(.*?)" id="contribution-day-component')
    count_pattern = re.compile(r'<tool-tip .*?class="sr-only position-absolute">(.*?) contribution')

    dates = date_pattern.findall(response.text)
    counts = count_pattern.findall(response.text)
    counts = [0 if c == "No" else int(c) for c in counts]

    if not dates or not counts:
        return {"total": 0, "contributions": []}

    contributions = [{"date": date, "count": count} for date, count in zip(dates, counts)]

    return {
        "total": sum(counts),
        "contributions": contributions
    }

class handler(BaseHTTPRequestHandler):
    def do_GET(self):
        parsed_url = urlparse(self.path)
        params = parse_qs(parsed_url.query)

        username = params.get('user', [''])[0]
        if not username:
            self.send_error(400, "Missing user parameter")
            return

        data = get_github_contributions(username)

        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode('utf-8'))
