from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import requests
import re
from typing import Dict

app = FastAPI(title="GitHub Calendar API", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["GET"],
    allow_headers=["*"],
)

def get_github_contributions(username: str) -> Dict:
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }

    try:
        response = requests.get(f"https://github.com/{username}", headers=headers)
        response.raise_for_status()
    except requests.RequestException:
        raise HTTPException(status_code=404, detail="User not found")

    date_pattern = re.compile(r'data-date="(.*?)" id="contribution-day-component')
    count_pattern = re.compile(r'<tool-tip .*?class="sr-only position-absolute">(.*?) contribution')

    dates = date_pattern.findall(response.text)
    counts = count_pattern.findall(response.text)
    counts = [0 if c == "No" else int(c) for c in counts]

    if not dates or not counts:
        return {"total": 0, "contributions": []}

    contributions = [{"date": date, "count": count} for date, count in zip(dates, counts)]

    return {
        "total": sum(counts),
        "contributions": contributions
    }

@app.get("/")
async def get_contributions(user: str):
    if not user:
        raise HTTPException(status_code=400, detail="Missing user parameter")

    return get_github_contributions(user)
