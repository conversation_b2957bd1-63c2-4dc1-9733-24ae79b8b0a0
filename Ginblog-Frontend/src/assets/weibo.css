
.weibo-new {
    background: #ff3852
}

.weibo-hot {
    background: #ff9406
}

.weibo-jyzy {
    background: #ffc000
}

.weibo-recommend {
    background: #00b7ee
}

.weibo-adrecommend {
    background: #febd22
}

.weibo-friend {
    background: #8fc21e
    
}

.weibo-boom {
    background: #bd0000
}

.weibo-topic {
    background: #ff6f49
}

.weibo-topic-ad {
    background: #4dadff
}

.weibo-boil {
    background: #f86400
}

#weibo .item-content {
    text-align: center;
}

#weibo-container {
    width: 100%;
    height: 180px;
    font-size: 95%;
    overflow-y: auto;
    -ms-overflow-style: none;
    scrollbar-width: none
}

.weibo-list-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: nowrap;
    font-size: 15px;
    margin: 8px 0;

}

.weibo-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: auto
}

.weibo-num {
    float: right
}

.weibo-hotness {
    display: inline-block;
    padding: 0 6px;
    transform: scale(.8) translateX(-3px);
    color: #fff;
    border-radius: 8px
}

#weibo-container a {
    color: #555;
}

[data-theme='dark'] #weibo-container a {
    color: rgba(255, 255, 255, 0.7);
}

#weibo-container::-webkit-scrollbar {
    display: none;
}