html,
body,
#app {
  height: 100%;
  margin: 0;
  padding: 0;
}

/* 滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: rgba(73, 177, 245, 0.2);
  border-radius: 2em;
}

::-webkit-scrollbar-thumb {
  background-color: #49b1f5;
  background-image: -webkit-linear-gradient(45deg,
      rgba(255, 255, 255, 0.4) 25%,
      transparent 25%,
      transparent 50%,
      rgba(255, 255, 255, 0.4) 50%,
      rgba(255, 255, 255, 0.4) 75%,
      transparent 75%,
      transparent);
  border-radius: 2em;
}

::-webkit-scrollbar-corner {
  background-color: transparent;
}

::-moz-selection {
  color: #fff;
  background-color: #49b1f5;
}

/*组件样式*/
.ant-layout {
  background-color: rgba(255, 255, 255, 0) !important;
}

.ant-message-notice .ant-message-notice-content {
  background: rgba(69, 184, 238, 0.5);
  border: 2px solid rgba(122, 168, 244, 1);
}

.ant-tooltip .ant-tooltip-inner {
  background-color: rgba(245, 245, 245, 1) !important;
}

.cateStyle {
  margin: 20px;
  padding: 1px;
  font-weight: 600;
  font-size: 20px;
  justify-content: center;
  border: 1px solid rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  box-shadow: 2px 2px 2px #338ded80;
  background-color: rgba(255, 255, 255, 0.75);

}

.cateStyle:hover {
  border: 1px solid #65b7e9;
}

.ant-menu-item {
  border-radius: 7px !important;
}

.ant-menu-item:hover {
  background: rgba(22, 119, 255, 0.5) !important;

  border-radius: 7px !important;
}

.ant-menu-item:hover .ant-menu-title-content {
  color: white;
}

.background {
  width: 100%;
  height: 100%;
  position: fixed;
  margin: 0 auto;
  top: 0;
  background: linear-gradient(70deg, rgb(16, 133, 222), pink);
  background-repeat: no-repeat;
  background-size: cover;
  opacity: 0.3;
  z-index: -1;
}

/*md渲染样式*/
.hljs {
  background: rgba(100, 75, 75, 0) !important;
  font-size: 15px;
}

.hljs .lang-python,
.hljs .lang-go,
.hljs .lang-cpp,
.hljs .lang-js {
  font-family: 'Fira Code', monospace !important;
}

.markdown-body pre {
  margin: 10px 0 !important;
  padding: 20px 15px !important;
  line-height: 1.6 !important;
  background-color: rgba(244, 244, 244, 0.75) !important;
  border-radius: 8px !important;
  border: 1px solid lightblue;
  box-shadow: 1px 1px 3px grey;
}

.markdown-body pre::before {
  background: #fc625d;
  border-radius: 50%;
  box-shadow: 20px 0 #fdbc40, 40px 0 #35cd4b;
  content: ' ';
  height: 12px;
  margin-left: -10px;
  margin-top: -15px;
  position: absolute;
  width: 12px;
}

.markdown-body blockquote {
  margin: 10px 0 !important;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 0px 5px 5px 0px;
  box-shadow: 0px 0px 4px grey;
  border-left: 3px solid rgba(70, 140, 221, 0.963) !important;
  padding: 10px !important;
}

mark {
  background-color: rgba(255, 255, 0, 0.75);
}


/*评论区样式*/
#twikoo {
  width: 90%;
}

/*ai-post*/
#summary-wrapper {
  width: 95%;
  border: rgba(30, 41, 59, calc(0.1 / 1)) solid 1px;
  padding: 0.5rem 1rem;
  margin-bottom: 0.8rem;
  border-radius: 16px;
}

#summary-wrapper>#post-ai-result {

  width: 100%;
  border: rgba(30, 41, 59, calc(0.1 / 1)) solid 1px;
  padding: 0.5rem 1rem;
  margin-bottom: 0.8rem;
  border-radius: 16px;
}

#summary-wrapper>#title {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.8rem;
  padding: 0 8px;
}

#summary-wrapper>#title>span.logo {
  background: rgba(59, 130, 246, 1);
  padding: 2px 10px;
  font-size: 12px;
  font-weight: 700;
  border: 1px solid transparent;
  border-radius: 25px;
  color: var(--tw-ring-offset-color);
  cursor: pointer;
}

#summary-wrapper>#title>span.logo.typing {
  animation: loading 1s infinite;
  cursor: not-allowed;
}

#summary-wrapper>#title>span.name {
  display: flex;
  align-items: center;
  color: rgba(59, 130, 246, 1);
  cursor: pointer;
}

#summary-wrapper>#title>span.name>.text {
  font-weight: bold;
}

#summary-wrapper>#title>span.name>.icon-up {
  font-weight: 400;
  font-size: 12px;
  margin-left: -3px;
  opacity: 0.8;
  transform: rotate(90deg);
}

#summary-wrapper>#title>span.name>.icon-robot {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 400;
  width: 25px;
  height: 26px;
  color: #fff;
  background-color: rgba(59, 130, 246, 1);
  border-radius: 50%;
  margin-right: 8px;
}

#summary-wrapper>#title>span.name>.icon-up svg {
  width: 1.1rem;
  height: 19px;
}

#summary-wrapper {
  background: rgba(148, 163, 184,
      calc(0.06 / 1));
}

#summary-wrapper>#meta {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-top: 1rem;
  padding: 0 8px;
  font-size: 12px;
}

#summary-wrapper>#meta>span.tip {
  opacity: 0.6;
}

#summary-wrapper>#meta>a.report {
  white-space: nowrap;
  margin-left: 12px;
  opacity: 0.8;
  transition: all 0.3s;
}

#summary-wrapper>#meta>a.report:hover {
  color: rgba(37, 99, 235, 1);
}

.post-ai-result {
  box-shadow: rgba(37, 99, 235,
      calc(0.08 / 1)) 0 0 2px 2px;
  margin-bottom: 0.9rem;
  min-height: 3rem;
  max-width: 100%;
  font-size: 16px;
  background-color: #fff;
}

.ai-cursor {
  display: inline-block;
  width: 3px;
  background: #333;
  height: 16px;
  margin-bottom: -2px;
  opacity: 0.95;
  margin-left: 3px;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  -ms-transition: all 0.3s;
  transition: all 0.3s;
  animation: blink 1s infinite;
}

@keyframes blink {

  0%,
  50%,
  100% {
    opacity: 1;
  }

  25%,
  75% {
    opacity: 0;
  }
}

@keyframes loading {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.4;
  }

  100% {
    opacity: 1;
  }
}