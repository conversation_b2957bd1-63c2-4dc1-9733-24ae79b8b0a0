<template>
    <div>
        <p style="font-size:25px; text-align: center;">明朝历史知识图谱</p>
        
        <cloud ></cloud>
        <div style="text-align: center;">
            <a-input-search v-model:value="queryInput" addon-before="请输入查询结点" 
            style=" width: 350px; margin: 10px 0;" @search="queryGraph" />
        </div>
        <div id="visualization"></div>
    </div>
</template>
  
<script setup>
import { onMounted, ref } from 'vue'
import axios from 'axios'
import { DataSet, Network } from 'vis-network/standalone'
import cloud from './Kword.vue'
const queryInput = ref('朱元璋');

var nodes = new DataSet();
var edges = new DataSet();
onMounted(() => {

    var container = document.getElementById('visualization')
    var data = {
        nodes: nodes,
        edges: edges
    };
    var options = {
        edges: {//关系线控制
            width: 2,//关系线宽度
            arrows: {//箭头
                to: {
                    enabled: false,//箭头是否显示、开启
                    scaleFactor: 0.5,//箭头的大小
                    type: 'arrow',//箭头的类型
                }
            }
        }
    }
    var network = new Network(container, data, options);
    queryGraph()

});
let queryGraph = async () => {

    const response = await axios.post('https://python-api.xwysyy.cn/knowledgeGraph/data', {
        data: queryInput.value
    });
    const data = response.data;
    // Assuming `nodes` and `edges` are accessible here, you might need to adjust scope or use reactive refs
    nodes.clear();
    edges.clear();
    nodes.add(data.nodes);
    edges.add(data.links);
}


</script>
  
<style>
#visualization {
    height: 800px;
    margin-top: 20px;
    border: 2px solid rgb(170, 166, 166);
    border-radius: 10px;
    box-shadow: 0px 0px 10px rgb(79, 79, 79);
    margin: 5px;
}

.vis-tooltip {
    white-space: normal !important;
    width: 400px;
}
</style>
  