<template>
  <!-- <div>
    <calendarHeatmap :end-date="endDate" :values="timeValue"
      :range-color="['#ebedf0', '#dae2ef', '#c0ddf9', '#73b3f3', '#3886e1', '#17459e']" />
  </div> -->
  <div>
    <calendarHeatmap :end-date="endDate" :values="timeValue"
      :range-color="['#ebedf0', '#ebedf0', '#9be9a8', '#40c463', '#30a14e', '#216e39']" />
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { CalendarHeatmap } from 'vue3-calendar-heatmap'
import http from 'axios'

const timeValue = ref([])
const endDate = new Date()
async function getTimeValue() {
  const { data: res } = await http.get('https://gitcalendar.xwysyy.cn/api/?user=xwysyy')
  timeValue.value = res.contributions
}
onMounted(() => {
  getTimeValue()
})
</script>

<style>
.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0
}

[data-tippy-root] {
  max-width: calc(100vw - 10px)
}

.tippy-box {
  position: relative;
  background-color: #333;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.4;
  white-space: normal;
  outline: 0;
  transition-property: transform, visibility, opacity
}

.tippy-box[data-placement^=top]>.tippy-arrow {
  bottom: 0
}

.tippy-box[data-placement^=top]>.tippy-arrow:before {
  bottom: -7px;
  left: 0;
  border-width: 8px 8px 0;
  border-top-color: initial;
  transform-origin: center top
}

.tippy-box[data-placement^=bottom]>.tippy-arrow {
  top: 0
}

.tippy-box[data-placement^=bottom]>.tippy-arrow:before {
  top: -7px;
  left: 0;
  border-width: 0 8px 8px;
  border-bottom-color: initial;
  transform-origin: center bottom
}

.tippy-box[data-placement^=left]>.tippy-arrow {
  right: 0
}

.tippy-box[data-placement^=left]>.tippy-arrow:before {
  border-width: 8px 0 8px 8px;
  border-left-color: initial;
  right: -7px;
  transform-origin: center left
}

.tippy-box[data-placement^=right]>.tippy-arrow {
  left: 0
}

.tippy-box[data-placement^=right]>.tippy-arrow:before {
  left: -7px;
  border-width: 8px 8px 8px 0;
  border-right-color: initial;
  transform-origin: center right
}

.tippy-box[data-inertia][data-state=visible] {
  transition-timing-function: cubic-bezier(.54, 1.5, .38, 1.11)
}

.tippy-arrow {
  width: 16px;
  height: 16px;
  color: #333
}

.tippy-arrow:before {
  content: "";
  position: absolute;
  border-color: transparent;
  border-style: solid
}

.tippy-content {
  position: relative;
  padding: 5px 9px;
  z-index: 1
}

.tippy-box[data-placement^=top]>.tippy-svg-arrow {
  bottom: 0
}

.tippy-box[data-placement^=top]>.tippy-svg-arrow:after,
.tippy-box[data-placement^=top]>.tippy-svg-arrow>svg {
  top: 16px;
  transform: rotate(180deg)
}

.tippy-box[data-placement^=bottom]>.tippy-svg-arrow {
  top: 0
}

.tippy-box[data-placement^=bottom]>.tippy-svg-arrow>svg {
  bottom: 16px
}

.tippy-box[data-placement^=left]>.tippy-svg-arrow {
  right: 0
}

.tippy-box[data-placement^=left]>.tippy-svg-arrow:after,
.tippy-box[data-placement^=left]>.tippy-svg-arrow>svg {
  transform: rotate(90deg);
  top: calc(50% - 3px);
  left: 11px
}

.tippy-box[data-placement^=right]>.tippy-svg-arrow {
  left: 0
}

.tippy-box[data-placement^=right]>.tippy-svg-arrow:after,
.tippy-box[data-placement^=right]>.tippy-svg-arrow>svg {
  transform: rotate(-90deg);
  top: calc(50% - 3px);
  right: 11px
}

.tippy-svg-arrow {
  width: 16px;
  height: 16px;
  fill: #333;
  text-align: initial
}

.tippy-svg-arrow,
.tippy-svg-arrow>svg {
  position: absolute
}

.vch__container .vch__legend {
  display: flex;
  justify-content: space-between;
  align-items: center
}

.vch__container .vch__external-legend-wrapper {
  margin: 0 8px
}

svg.vch__wrapper {
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Open Sans, Helvetica Neue, sans-serif;
  line-height: 10px;
  width: 100%
}

svg.vch__wrapper .vch__months__labels__wrapper text.vch__month__label {
  font-size: 10px
}

svg.vch__wrapper .vch__days__labels__wrapper text.vch__day__label,
svg.vch__wrapper .vch__legend__wrapper text {
  font-size: 9px
}

svg.vch__wrapper text.vch__month__label,
svg.vch__wrapper text.vch__day__label,
svg.vch__wrapper .vch__legend__wrapper text {
  fill: #767676
}

svg.vch__wrapper rect.vch__day__square:hover {
  stroke: #555;
  stroke-width: 2px;
  paint-order: stroke
}

svg.vch__wrapper rect.vch__day__square:focus {
  outline: none
}

svg.vch__wrapper.dark-mode text.vch__month__label,
svg.vch__wrapper.dark-mode text.vch__day__label,
svg.vch__wrapper.dark-mode .vch__legend__wrapper text {
  fill: #fff
}
</style>