

/* 随机友链文章卡片 */
#random-article {
    display: flex;
    position: relative;
    width: 100%;
    margin: 8px 0;
    border-radius: 10px;
    height: 210px;
    background-color: rgba(255, 255, 255, 0.5);
    box-shadow: 1px 1px 5px rgb(121, 207, 250);
    border: 1px solid rgb(185, 213, 227);
}
#random-article:hover {
    box-shadow: 1px 1px 10px rgb(121, 207, 250);
}

.random-container {
    position: relative;
    margin: 20px;
    width: 90%;
    height: 170px;
}

.random-container:hover .random-title {
    font-size: 32px;
}

.random-author {
    font-size: 14px;
    color: gray;
    margin-bottom: 10px;
}

.random-container-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
}

.random-title {
    margin-bottom: 10px;
    font-size: 30px;
    transition: font-size 0.3s ease-in-out;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.random-link-button {
    position: absolute;
    bottom: 20px;
    right: 20px;
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    background-color: #3498db;
    color: #fff;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease-in-out;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.5);
    -webkit-backdrop-filter: blur(25px);
    backdrop-filter: blur(25px);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s;
}

.modal-open {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 350px;
    background-color: rgba(239, 250, 255, 0.7);
    padding: 20px;
    border: 1px solid #e3e8f7;
    z-index: 1000;
    max-height: 90%;
    overflow-y: auto;
    border-radius: 20px;
    transition: opacity 0.3s;
}

@media screen and (max-width: 440px) {
    .modal-content {
        width: 80%;
    }
}

#modal-author-avatar {
    display: block;
    margin: 0 auto;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    object-fit: cover;
}

#modal-author-name-link {
    display: block;
    text-align: center;
    font-size: 15px;
    margin: 15px 0;
    color: #5eb1e8;
    text-decoration: none;
}

#modal-author-name-link:hover {
    text-decoration: underline;
}

.modal-content hr {
    margin: 20px 0;
}

#modal-articles-container {
    border-top: #3498db double 2px;
    margin-top: 20px;
    padding-top: 10px;
}

.modal-article {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: #3498db dashed 1px;
}

.modal-article .modal-article-title {
    color: #000000;
    font-size: 18px;
    line-height: 1.2;
    cursor: pointer;
    height: 2.5em;
    width: 100%;
    margin-bottom: 5px;
    text-decoration: none;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.modal-article .modal-article-title:hover {
    color: #3498db;
    text-decoration: underline;
}

.modal-article .modal-article-date {
    font-size: 12px;
    width: 100%;
    color: gray;
    padding: 5px;
    cursor: default;
    text-align: right;
}

/* 其他样式... */
.articles-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 8px;
    width: 100%;
}

.card {
    border-radius: 10px;
    padding: 10px;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 150px;
    background-color: rgba(255, 255, 255, 0.5);
    box-shadow: 1px 1px 1px rgb(121, 207, 250);
    border: 1px solid rgb(185, 213, 227);
    transition: border 0.3s;
}
.card:hover {
    box-shadow: 1px 1px 5px rgb(121, 207, 250);
    border: 1px solid #5eb1e8;
}
.card-title {
    z-index: 1;
    font-size: 17px;
    color: #000000;
    font-weight: 520;
    cursor: pointer;
    margin-bottom: 10px;
    line-height: 1.5;
    max-height: 4.5em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    transition: color 0.3s;
}

.card-title:hover {
    color: #3498db;
    text-decoration: underline;
}

.card-author,
.card-date {
    font-size: 12px;
    color: gray;
    padding: 5px;
    transition: box-shadow 0.2s;
}

.card-author:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.card-author {
    cursor: pointer;
    background-color: #f4f4f9;
    border: 1px solid #e3e8f7;
    border-radius: 15px;
    display: flex;
    padding-right: 10px;
    width: fit-content;
    height: 26px;
    align-items: center;
}

#friend-circle-lite-root .card-author img {
    border-radius: 50%;
    width: 15px;
    height: 15px;
    margin: 0 2px !important;
    object-fit: cover;
}

.card-date {
    position: absolute;
    z-index: 1;
    bottom: 5px;
    cursor: default;
    right: 10px;
    display: flex;
    align-items: center;
}

#friend-circle-lite-root .card-bg {
    cursor: default;
    z-index: 0;
    border-radius: 50%;
    margin: 0;
    position: absolute;
    bottom: -20px;
    right: -16px;
    width: 140px;
    height: 140px;
    opacity: 0.4;
    transition: transform 0.6s ease, bottom 0.3s ease, right 0.3s ease; /* 0.3秒的平滑过渡效果 */
}

#friend-circle-lite-root .card:hover .card-bg {
    transform: scale(1.1);
    bottom: -10px;
    right: -8px;
}

#load-more-btn {
    color: #000000;
    font-size: 15px;
    background-color: white;
    cursor: pointer;
    width: 200px;
    border-radius: 20px;
    border: 1px solid #e3e8f7;
    padding: 3px;
    transition: all 0.3s;
    margin: 20px auto;
    display: block;
}

#load-more-btn:hover {
    background-color: #3498db;
    width: 300px;
    color: white;
}

#stats-container {
    font-size: 13px;
    text-align: right;
    margin-top: 20px;
}

#stats-container > * {
    margin-bottom: 3px;
}

#stats-container a {
    color: gray;
    text-decoration: none;
}
