<template>
    <div>
        <div id="friend-circle-lite-root"></div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue';

onMounted(() => {
    window.UserConfig = {
        private_api_url: 'https://friend.xwysyy.cn/',
        page_turning_number: 24,
        error_img: 'https://i.p-i.vip/30/20240815-66bced9226a36.webp'
    }
    const script = document.createElement('script');
    script.src = '/js/friendCircle/fclite.js';
    script.async = true;
    document.head.appendChild(script);
})
</script>

<style>
@import url("fclite.css");
</style>
