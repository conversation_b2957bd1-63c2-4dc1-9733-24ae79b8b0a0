<template>
    <div class="cardStyle">
        <p class="text-3xl/7 font-semibold hover:text-sky-600 hover:font-bold">{{ profileInfo.name }}</p>
        <a-avatar :size="100" :src="profileInfo.avatar" class="avatar" />
        <p class="text-lg"><span v-html="profileInfo.desc"
                class="font-kaiti text-xl font-semibold hover:text-orange-500"></span></p>
        <p class="text-lg">
            <MailOutlined /> {{ profileInfo.email }}
        </p>
        <p class="text-lg">
            <GithubOutlined /> {{ profileInfo.github }}
        </p>
    </div>
    <div class="cardStyle">
        <ipWelcome class="mx-6 mb-4 mt-1" />
    </div>

    <div class="cardStyle">
        <span class="text-2xl/10 font-semibold font-kaiti">微博热搜</span>
        <div id="weiboContent" class="mx-6 mb-4 mt-1"></div>
    </div>
    <div class="cardStyle">
        <span class="text-2xl/10 font-semibold font-kaiti">每日善良</span>
        <div class="mx-6 mb-4 mt-1 text-xl font-kaiti">
            {{ yiyan }}
        </div>
    </div>
    <div class="cardStyle">
        <span class="text-2xl/10 font-semibold font-kaiti">词云图</span>
        <wordcloud />
    </div>
</template>
<script setup>
import { MailOutlined, GithubOutlined } from '@ant-design/icons-vue'
import '../../assets/weibo.css'
import { onMounted, ref } from 'vue'
import http from '../../plugin/http'

import wordcloud from '../charts/Wordcloud.vue'
import ipWelcome from './sidebar/ipWelcome.vue'

let profileInfo = ref({
    id: 1
})
// 获取个人设置
async function getProfileInfo() {
    const { data: res } = await http.get(
        `profile/1`
    )
    profileInfo.value = res.data
}


/*微博热搜 */
const getData = () => {
    fetch('https://python-api.xwysyy.cn/weibo').then(data => data.json()).then(data => {
        data = { time: Date.now(), ls: JSON.stringify(data) }
        localStorage.setItem('weibo', JSON.stringify(data))
    }).then(weibo)
}
const weibo = () => {
    let hotness = {
        '爆': 'weibo-boom',
        '热': 'weibo-hot',
        '沸': 'weibo-boil',
        '新': 'weibo-new',
        '荐': 'weibo-recommend',
        '影': 'weibo-jyzy',
        '剧': 'weibo-jyzy',
        '综': 'weibo-jyzy'
    }
    let html = '<div id="weibo-container">'
    let data = JSON.parse(localStorage.getItem('weibo'));
    let nowTime = Date.now();
    let ls;
    if (data == null || nowTime - data.time > 600000) {
        getData()
        return
    } else {
        ls = JSON.parse(data.ls)
    }
    for (let item of ls) {
        html += '<div class="weibo-list-item"><div class="weibo-hotness ' + hotness[(item.hot || '荐')] + '">' + (item.hot || '荐') + '</div>' +
            '<span class="weibo-title"><a title="' + item.title + '"href="' + item.url + '" target="_blank" rel="external nofollow noreferrer">' + item.title + '</a></span>' +
            '<div class="weibo-num"><span>' + item.num + '</span></div></div>'
    }
    html += '</div>'
    document.getElementById('weiboContent').innerHTML = html
}

/*一言 */
const yiyan = ref('')
async function GetYiyan() {
    fetch('https://python-api.xwysyy.cn/yiyan').then(res => res.json()).then(res => {
        yiyan.value = res.data.content
    })
}

onMounted(() => {
    getProfileInfo()
    weibo()
    GetYiyan()
})
</script>
<style scoped>
.cardStyle {
    background-color: rgba(255, 255, 255, 0.5);
    margin: 15px;
    text-align: center;
    justify-self: center;
    width: 80%;
    box-shadow: 1px 1px 5px rgb(121, 207, 250);
    border: 1px solid rgb(185, 213, 227);
    border-radius: 15px;
}

.cardStyle:hover {
    box-shadow: 1px 1px 10px rgb(121, 207, 250);
}

.avatar:hover {
    transform: rotate(360deg);
    transition: transform 1s;
}
</style>