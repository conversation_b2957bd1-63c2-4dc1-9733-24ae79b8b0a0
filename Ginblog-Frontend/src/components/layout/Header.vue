<template>
  <!-- pc端 -->
  <template v-if="windowSize > 960">
    <a-row :gutter="8">
      <a-col :span="4">
        <a-space size="middle">
          <a-avatar :size="48" src="/favicon.ico" class="avatar" />
          <span v-if="windowSize > 1400" class="text-2xl font-kaiti font-semibold">善良的xwysyy</span>
          <span v-else class="text-2xl font-kaiti font-semibold">xwysyy</span>
        </a-space>
      </a-col>
      <a-col :span="14">
        <a-menu v-model:selectedKeys="current" mode="horizontal" @click="goToPage" :items="items"
          class="text-2xl font-kaiti font-bold bg-white/0"></a-menu>
      </a-col>
      <a-col :span="6">
        <a-input-search v-model:value="searchTitle" placeholder="请输入文章标题" style="vertical-align: middle;"
          @search="onSearch(searchTitle)" />
      </a-col>

    </a-row>
  </template>
  <!-- 移动端 -->
  <template v-else>
    <a-row>
      <a-col :span="12" style="text-align: left;">
        <a-button @click="open = true">
          <MenuUnfoldOutlined /><span class="text-base font-kaiti font-semibold">菜单</span>
        </a-button>
        <a-drawer :open="open" title="菜单" @close="open = false" placement="left">
          <a-menu v-model:selectedKeys="current" :items="items" mode="inline" @click="goToPage"></a-menu>
        </a-drawer>
      </a-col>
      <a-col :span="12" style="text-align: right;">
        <a-space size="middle">
          <span class="text-2xl font-kaiti font-semibold">xwysyy</span>
          <a-avatar :size="40" src="/favicon.ico" class="avatar">
          </a-avatar>
        </a-space>
      </a-col>
    </a-row>
  </template>

</template>

<script setup>
import { message } from 'ant-design-vue'
import { h, ref } from 'vue'
import { HomeOutlined, SettingOutlined, MenuUnfoldOutlined, FolderOpenOutlined, EllipsisOutlined, ShareAltOutlined, InfoCircleOutlined } from '@ant-design/icons-vue'
import { useWindowSize } from '@vueuse/core'
import router from '@/router'
const current = ref(['index'])
let windowSize = useWindowSize().width
const open = ref(false)
const searchTitle = ref('')
const items = ref([
  {
    key: 'index',
    icon: () => h(HomeOutlined, { style: { fontSize: '25px' } }),
    label: '首页',
    title: 'Index',
  },
  {
    key: 'log',
    icon: () => h(FolderOpenOutlined, { style: { fontSize: '25px' } }),
    label: '日志',
    title: 'log',
  },
  {
    key: 'admin',
    icon: () => h(SettingOutlined, { style: { fontSize: '25px' } }),
    label: '管理',
    title: 'admin',
  },
  {
    key: 'about',
    icon: () => h(InfoCircleOutlined, { style: { fontSize: '25px' } }),
    label: '关于',
    title: 'about',
  },
  {
    key: 'friend',
    icon: () => h(ShareAltOutlined, { style: { fontSize: '25px' } }),
    label: '订阅',
    title: 'friend',
  },
  {
    key: 'other',
    icon: () => h(EllipsisOutlined, { style: { fontSize: '25px' } }),
    label: '其他',
    title: 'other',
    children: [
      {
        type: 'group',
        label: 'project',
        children: [
          {
            label: '知识图谱',
            key: 'knowledgeGraph',
          },
          {
            label: 'Github贡献热力图',
            key: 'githubCalendar',
          }
        ],
      },
      // {
      //   type: 'group',
      //   label: 'Item 2',
      //   children: [
      //     {
      //       label: 'Option 3',
      //       key: 'setting:3',
      //     },
      //     {
      //       label: 'Option 4',
      //       key: 'setting:4',
      //     },
      //   ],
      // },
    ],
  }
]);

const onSearch = (title) => {
  if (title.length == 0)
    return message.error('你还没填入搜索内容哦')
  router.push(`/search/${title}`)
}
const goToPage = (item) => {
  if (item.key === 'admin') {
    window.open('https://blog-admin.xwysyy.cn', '_blank')
  } else {
    router.push('/' + item.key).catch((err) => err)
  }
}
</script>
<style scoped>
.avatar:hover {
  transform: rotate(360deg);
  transition: transform 1s;
}
</style>