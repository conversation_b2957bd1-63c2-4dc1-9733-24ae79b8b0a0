<template>
    <div>
        <div class="timeline">
            <div class="timeline-item" v-for="log in logs" :key="log.date">
                <div class="logStyle">
                    <span class="timeline-date">{{ log.date }}</span>
                    <ol>
                        <li v-for="item in log.items" :key="item">{{ item }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
const logs = [
    {
        date: '2024-09-04',
        items: [
            '文章详情页面添加了文章摘要',
        ]
    },
    {
        date: '2024-08-02',
        items: [
            '新增了关于页面',
            '首页文章卡片样式调整, 增加了擦亮效果',
        ]
    },
    {
        date: '2024-07-31',
        items: [
            '更新了后台管理界面样式',
            '重构了后台管理界面代码',
            '添加了友链朋友圈界面'
        ]
    },
    {
        date: '2024-04-10',
        items: [
            '更新了页脚徽标',
            '增添blog域名为www.xwysyy.cn'
        ]
    },
    {
        date: '2024-03-31',
        items: [
            '新增了github贡献热力图',
            '新增了neo4j知识图谱',
            '修改日志页面样式',
        ]
    },
    {
        date: '2024-03-02',
        items: [
            '添加目录，点击右下角悬浮按钮可打开',
            '使用正则表达式，实现文章字数统计'
        ]
    },
    {
        date: '2024-02-29',
        items: [
            '修改代码块样式为mac风格',
            '优化单页面文章数显示, 根据pc和移动端动态改变',
            '增添日志界面'
        ]
    }
]
</script>

<style scoped>
.timeline {
    position: relative;
    margin: 10px 0;
}

.timeline:before {
    content: '';
    position: absolute;
    top: 0;
    left: 10px;
    width: 2px;
    height: 100%;
    border-left: 2px dashed #60b6ef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
    padding-left: 40px;
}

.timeline-date {
    position: absolute;
    font-size: 18px;
    margin: 0px;
    font-weight: bold;
    color: #3c82eb;
    border-radius: 5px;
}

.logStyle {
    width: 80%;
    padding: 10px 10px;
    margin: 20px;
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 0.5);
    font-size: 16px;
    line-height: 1.5;
    word-break: break-all;
    white-space: pre-wrap;
    animation: glow 2s infinite alternate;
}

@keyframes glow {
    0% {
        box-shadow: 0 0 5px rgba(121, 187, 234, 0.5);
    }

    50% {
        box-shadow: 0 0 10px rgba(89, 180, 236, 0.8);
    }

    100% {
        box-shadow: 0 0 5px rgba(121, 187, 234, 0.5);
    }
}

.logStyle ol {
    list-style-type: none;
    counter-reset: item;
    padding-top: 20px;
}

.logStyle ol li {
    position: relative;
    padding-left: 15px;
    margin-bottom: 10px;
}

.logStyle ol li:before {
    content: counter(item);
    counter-increment: item;
    position: absolute;
    left: 0;
    top: 0;
    font-weight: bold;
    color: #418eed;
}
</style>