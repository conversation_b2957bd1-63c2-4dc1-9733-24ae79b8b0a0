<template>
    <div class="flex items-center flex-col">
        <div class="mt-4">
            <div class="author-info">
                <div class="author-tag-left">
                    <span class="author-tag">🤖️ 数码科技爱好者</span>
                    <span class="author-tag">🏠 智能家居小能手</span>
                    <span class="author-tag">🔨 设计开发全栈</span>
                    <span class="author-tag">🔍 不解决不放弃</span>
                </div>
                <div class="author-img">
                    <img id="avatar" src="/favicon.ico" alt="avatar" />
                </div>
                <div class="author-tag-right">
                    <span class="author-tag">专修交互与设计 🤝</span>
                    <span class="author-tag">脚踏实地行动派 🏃</span>
                    <span class="author-tag">践踏各种困难 🧱</span>
                    <span class="author-tag">一个善良的人 💢</span>
                </div>
            </div>
        </div>
        <div class="text-4xl font-bold mt-2">关于本站</div>
        <div class="w-full">

            <div class="author-page-content">
                <div class="author-content mt-5">
                    <div class="author-content-item Hello">
                        <div class="title1">你好，很高兴认识你👋</div>
                        <div class="title2">我是 <span class="inline-word">xwysyy</span></div>
                        <div class="title1">是一个善良的人</div>
                    </div>
                    <div class="aboutsiteTips author-content-item">
                        <div class="author-content-item-tips">追求</div>
                        <h2>源于<br> 热爱而去<span class="inline-word">创造</span>
                            <div class="mask">
                                <span class="first-tips">程序</span>
                                <span>生活</span>
                                <span data-up>产品</span>
                                <span data-show>设计</span>
                            </div>
                        </h2>
                    </div>
                </div>

                <!-- 技能展示区域 -->
                <div class="skills-section mt-8">
                    <div class="skills-title">
                        <h3 class="text-3xl font-bold mb-4">技能开启创造力</h3>
                    </div>
                    <div class="skills-grid">
                        <div 
                            class="skill-item" 
                            v-for="(skill, index) in skills" 
                            :key="skill.name"
                            :style="{ 'animation-delay': `${index * 0.1}s` }"
                            :data-category="skill.category"
                        >
                            <div class="skill-icon">
                                <Icon :icon="skill.icon" class="skill-iconify" />
                            </div>
                            <div class="skill-name">{{ skill.name }}</div>
                            <div class="skill-glow"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { Icon } from '@iconify/vue'

// 技能数据
const skills = ref([
    // 前端开发
    { name: 'Vite.js', icon: 'logos:vitejs', category: 'frontend' },
    { name: 'Vue.js', icon: 'logos:vue', category: 'frontend' },
    { name: 'Ant-design', icon: 'logos:ant-design', category: 'frontend' },
    { name: 'CSS', icon: 'logos:css-3', category: 'frontend' },
    { name: 'HTML', icon: 'logos:html-5', category: 'frontend' },
    { name: 'JavaScript', icon: 'logos:javascript', category: 'frontend' },
    { name: 'TypeScript', icon: 'logos:typescript-icon', category: 'frontend' },
    { name: 'Nginx', icon: 'logos:nginx', category: 'frontend' },
    { name: 'Electron', icon: 'logos:electron', category: 'frontend' },
    { name: 'TailwindCSS', icon: 'logos:tailwindcss-icon', category: 'frontend' },
    
    // 后端开发
    { name: 'Node.js', icon: 'logos:nodejs-icon', category: 'backend' },
    { name: 'Python', icon: 'logos:python', category: 'backend' },
    { name: 'Go', icon: 'logos:go', category: 'backend' },
    { name: 'FastAPI', icon: 'simple-icons:fastapi', category: 'backend' },
    
    // 数据库
    { name: 'Redis', icon: 'logos:redis', category: 'database' },
    { name: 'MySQL', icon: 'logos:mysql', category: 'database' },
    { name: 'Neo4j', icon: 'logos:neo4j', category: 'database' },
    
    // DevOps & 云服务
    { name: 'Docker', icon: 'logos:docker-icon', category: 'devops' },
    { name: 'GitHub Actions', icon: 'logos:github-actions', category: 'devops' },
    { name: 'Cloudflare', icon: 'logos:cloudflare-icon', category: 'devops' },
    
    // API & 工具
    { name: 'REST', icon: 'mdi:api', category: 'api' },
    { name: 'Postman', icon: 'logos:postman-icon', category: 'api' },
    
    // 版本控制
    { name: 'Git', icon: 'logos:git-icon', category: 'vcs' },
    { name: 'GitHub', icon: 'logos:github-icon', category: 'vcs' },
    
    // 开发工具
    { name: 'VSCode', icon: 'logos:visual-studio-code', category: 'tools' },
    { name: 'Sublime Text', icon: 'logos:sublimetext-icon', category: 'tools' }
])

const tipsChange = setInterval(function () {
    const show = document.querySelector('span[data-show]');
    const next = show?.nextElementSibling || document.querySelector('.first-tips');
    const up = document.querySelector('span[data-up]');
    if (up) {
        up.removeAttribute('data-up');
    }
    if (show) {
        show.removeAttribute('data-show');
        show.setAttribute('data-up', '');
    }
    if (next) {
        next.setAttribute('data-show', '');
    }
}, 4000);

// 清理定时器
if (typeof document !== 'undefined') {
    document.addEventListener('pjax:send', function () {
        clearInterval(tipsChange);
    });
}
</script>

<style scoped>
@keyframes floating {
    0% {
        transform: translate(0, -4px);
    }

    50% {
        transform: translate(0, 4px);
    }

    100% {
        transform: translate(0, -4px);
    }
}

.author-info {
    display: flex;
    align-items: center;
    margin: 0 0 16px 0;
}

.author-tag-left {
    display: flex;
    flex-direction: column;
    align-items: flex-end
}

.author-tag-right {
    display: flex;
    flex-direction: column;
    align-items: flex-start
}

.author-tag-left .author-tag:first-child,
.author-tag-left .author-tag:last-child {
    margin-right: -16px
}

.author-tag-right .author-tag:first-child,
.author-tag-right .author-tag:last-child {
    margin-left: -16px
}

.author-tag {
    transform: translate(0, -4px);
    padding: 1px 8px;
    background: rgba(255, 255, 255, 0.75);
    border-radius: 40px;
    margin-top: 6px;
    font-size: 14px;
    font-weight: 700;
    box-shadow: 0 5px 12px -5px rgba(102, 68, 68, 0.2);
    animation: 6s ease-in-out 0s infinite normal none running floating;
}

.author-tag:nth-child(1) {
    animation-delay: 0s;
}

.author-tag:nth-child(2) {
    animation-delay: .6s
}

.author-tag:nth-child(3) {
    animation-delay: 1.2s
}

.author-tag:nth-child(4) {
    animation-delay: 1.8s
}

.author-img {
    margin: 0 30px;
    border-radius: 50%;
    width: 180px;
    height: 180px;
    position: relative;
    background: #f7f7f9;
    user-select: none;
    transition: .3s
}

.author-img #avatar {
    border-radius: 50%;
    overflow: hidden;
    width: 180px;
    height: 180px
}

.author-img:hover {
    transform: scale(1.1);
    transition: .4s
}

.author-img::before {
    content: '';
    -webkit-transition: 1s;
    -moz-transition: 1s;
    -o-transition: 1s;
    -ms-transition: 1s;
    transition: 1s;
    width: 30px;
    height: 30px;
    background: rgb(97, 215, 118);
    position: absolute;
    border: 2px solid #fff;
    border-radius: 50%;
    bottom: 5px;
    right: 10px;
    z-index: 2
}

@property --angle {
    syntax: '<angle>';
    initial-value: 0deg;
    inherits: false;
}

@keyframes spin {
    from {
        --angle: 0deg;
    }

    to {
        --angle: 360deg;
    }
}

.author-img::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    background-image: conic-gradient(from var(--angle), transparent 50%, rgb(47, 119, 244));
    animation: 4s spin linear infinite;
    border-radius: 50%;
    filter: blur(2px);
    z-index: -1;
    padding: 4px;
}

.author-page-content {
    display: flex;
    flex-direction: column;
    gap: .5rem;
}

.author-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
    gap: .5rem;
}

.author-content-item {
    width: 49%;
    background: rgba(255, 255, 255, 0.75);
    border: 1px solid rgb(185, 213, 227);
    border-radius: 12px;
    box-shadow: 1px 1px 2px rgb(121, 207, 250);
    position: relative;
    padding: 0.3rem 2rem;
    overflow: hidden;
    flex: 1;
}

.author-content-item:hover {
    box-shadow: 1px 1px 5px rgb(121, 207, 250);
}

.Hello {
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: #fff;
    background: linear-gradient(120deg, #5b27ff 0, #00d4ff 100%);
    background-size: 200%;
    min-height: 100px;
    flex: 4;
}

.Hello .title1 {
    opacity: 0.8;
    line-height: 1.3;
    font-size: 16px;
}

.Hello .title2 {
    font-size: 36px;
    font-weight: 700;
    line-height: 1.1;
    margin: .5rem 0;
}

.inline-word {
    word-break: keep-all;
    white-space: nowrap;
}

.author-content-item.aboutsiteTips {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-direction: column;
    flex: 3;
}

.aboutsiteTips h2 {
    margin-right: auto;
    font-size: 36px;
    font-family: Helvetica;
    line-height: 1.06;
    letter-spacing: -.02em;
    margin-top: 0
}

.author-content-item .author-content-item-tips {
    opacity: .8;
    font-size: .8rem;
    margin-bottom: .5rem;
}

.aboutsiteTips .mask {
    height: 36px;
    position: relative;
    overflow: hidden;
    margin-top: 4px;
}

.aboutsiteTips .mask span {
    display: block;
    box-sizing: border-box;
    position: absolute;
    top: 36px;
    padding-bottom: 0px;
    background-size: 100% 100%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    background-repeat: no-repeat
}

.aboutsiteTips .mask span[data-up] {
    transform: translateY(-200%);
    transition: 0.5s transform ease-in-out;
}

.aboutsiteTips .mask span[data-show] {
    transform: translateY(-100%);
    transition: 0.5s transform ease-in-out;
}

.aboutsiteTips .mask span:nth-child(1) {
    background-image: linear-gradient(45deg, #0ecffe 50%, #07a6f1)
}

.aboutsiteTips .mask span:nth-child(2) {
    background-image: linear-gradient(45deg, #18e198 50%, #0ec15d)
}

.aboutsiteTips .mask span:nth-child(3) {
    background-image: linear-gradient(45deg, #8a7cfb 50%, #633e9c)
}

.aboutsiteTips .mask span:nth-child(4) {
    background-image: linear-gradient(45deg, #fa7671 50%, #f45f7f)
}

/* 技能展示区域 */
.skills-section {
    width: 100%;
}

.skills-title {
    text-align: center;
    margin-bottom: 1.5rem;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
    gap: 0.8rem;
    margin-bottom: 2rem;
}

.skill-item {
    background: rgba(255, 255, 255, 0.75);
    border: 1px solid rgb(185, 213, 227);
    border-radius: 12px;
    padding: 0.8rem;
    text-align: center;
    box-shadow: 1px 1px 2px rgb(121, 207, 250);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-height: 90px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transform: translateY(30px) scale(0.9);
    animation: skillFadeInUp 0.8s ease-out forwards;
    cursor: pointer;
}

.skill-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.skill-item:hover::before {
    left: 100%;
}

.skill-item:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 10px 25px rgba(121, 207, 250, 0.4);
    border-color: rgb(47, 119, 244);
    background: rgba(255, 255, 255, 0.9);
}

.skill-item:hover .skill-glow {
    opacity: 1;
    transform: scale(1);
}

.skill-icon {
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.skill-iconify {
    width: 36px;
    height: 36px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.skill-item:hover .skill-iconify {
    transform: scale(1.2) rotateY(360deg);
    filter: drop-shadow(0 4px 8px rgba(47, 119, 244, 0.3));
}

.skill-name {
    font-size: 0.75rem;
    font-weight: 600;
    color: #333;
    margin-top: 0.3rem;
    line-height: 1.2;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.skill-item:hover .skill-name {
    color: rgb(47, 119, 244);
    font-weight: 700;
}

.skill-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(47, 119, 244, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
    transition: all 0.4s ease;
    z-index: 1;
}

/* 技能卡片入场动画 */
@keyframes skillFadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 技能标题动画 */
.skills-title h3 {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    from {
        text-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
    }
    to {
        text-shadow: 0 0 30px rgba(118, 75, 162, 0.8);
    }
}

/* 技能网格容器动画 */
.skills-grid {
    animation: gridFadeIn 1s ease-out;
}

@keyframes gridFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .author-content {
        flex-direction: column;
    }
    
    .author-content-item {
        width: 100%;
    }
    
    .skills-grid {
        grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
        gap: 0.6rem;
    }
    
    .skill-iconify {
        width: 32px;
        height: 32px;
    }
    
    .skill-item:hover {
        transform: translateY(-5px) scale(1.02);
    }
    
    .skill-item:hover .skill-iconify {
        transform: scale(1.15) rotateY(180deg);
    }
}

/* 点击波纹效果 */
.skill-item {
    position: relative;
    overflow: hidden;
}

.skill-item::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(47, 119, 244, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.skill-item:active::after {
    width: 200px;
    height: 200px;
}

/* 技能分类颜色主题（可选） */
.skill-item[data-category="frontend"]:hover {
    border-color: #61dafb;
    box-shadow: 0 10px 25px rgba(97, 218, 251, 0.3);
}

.skill-item[data-category="backend"]:hover {
    border-color: #68d391;
    box-shadow: 0 10px 25px rgba(104, 211, 145, 0.3);
}

.skill-item[data-category="database"]:hover {
    border-color: #f6ad55;
    box-shadow: 0 10px 25px rgba(246, 173, 85, 0.3);
}

.skill-item[data-category="devops"]:hover {
    border-color: #9f7aea;
    box-shadow: 0 10px 25px rgba(159, 122, 234, 0.3);
}

.skill-item[data-category="tools"]:hover,
.skill-item[data-category="api"]:hover,
.skill-item[data-category="vcs"]:hover {
    border-color: #ed8936;
    box-shadow: 0 10px 25px rgba(237, 137, 54, 0.3);
}
</style>