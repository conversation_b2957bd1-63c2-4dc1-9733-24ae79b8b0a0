<template>
  <div style="font-size: 45px; text-align: center; font-weight: 800; margin-top: 10px; font-family: SimHei">{{
    artInfo.title }}</div>
  <a-divider></a-divider>
  <div style="text-align: center;">
    <span style="font-size: 16px; margin-right: 25px;">
      <CalendarTwoTone style="margin-right: 5px;" />{{ date }}
    </span>
    <span style="font-size: 16px; margin-right: 25px;">
      <EyeTwoTone style="margin-right: 5px;" />{{ artInfo.read_count }}
    </span>
    <span style="font-size: 16px;">
      <EditTwoTone style="margin-right: 5px;" />{{ countWord }}
    </span>
  </div>
  <a-divider></a-divider>
  <!-- <div id="summary-wrapper">
    <div id="title">
      <span id="summary-post" class="name">
        <span class="icon-robot">1</span>
        <span class="text"> 文章摘要 </span>
        <span class="icon-up">1</span>
      </span>
      <span id="ai-logo" class="logo"> xwysyyGPT </span>
    </div>
    <div id="post-ai-result" class="post-ai-result">
      <span id="post-ai-result-text" lass="text">
        <span id="result-loading">加载中...</span>
      </span>
    </div>
    <div id="meta">
      <span class="tip">此内容根据文章生成，并经过人工审核，仅用于文章内容的解释与总结</span>
      <a class="report">投诉</a>
    </div>
  </div> -->

  <a-alert :message="artInfo.desc" style="margin-bottom: 15px; width:90%; font-size: 15px;">{{ artInfo.desc }}</a-alert>

  <div id="editor">
    <!-- <mavonEditor style="height: 100%;"
     v-model="artInfo.content" :subfield="false" :defaultOpen="'preview'" :toolbarsFlag="false" :navigation="true"
       :transition="false" ></mavonEditor> -->
    <mavonEditor style="height: 100%; display: none;"></mavonEditor>
  </div>
  <div id="contentRef" v-html="artInfo.blog" class="markdown-body" style="text-align:left;width:90%;"></div>
  <a-divider></a-divider>
  <div id="twikoo-comment"></div>
  <a-float-button @click="() => tocOpen = true" style="bottom:75px;"
    :class="{ 'Left': windowSizeWidth <= 960, 'Right': windowSizeWidth > 960 }">
    <template #icon>
      <UnorderedListOutlined />
    </template>
  </a-float-button>
  <div>
    <a-modal v-model:open="tocOpen" :footer="null">
      <KilaKilaCatalog container="#contentRef" v-if="artLoaded"></KilaKilaCatalog>
    </a-modal>
  </div>
</template>

<script setup>
import 'mavon-editor/dist/css/index.css'
import '../../assets/github-markdown.min.css'
import http from "../../plugin/http"
import { mavonEditor } from 'mavon-editor'
import dayjs from 'dayjs'
import { EyeTwoTone, EditTwoTone, CalendarTwoTone, UnorderedListOutlined } from '@ant-design/icons-vue'
import { onMounted, reactive, ref } from 'vue'
import KilaKilaCatalog from '../layout/sidebar/TOC.vue'
import { useWindowSize } from '@vueuse/core'
let windowSizeWidth = useWindowSize().width

const props = defineProps(['id'])
let artInfo = reactive({})
const total = ref(0)
const date = ref('')
const artLoaded = ref(false)
const tocOpen = ref(false)
const countWord = ref(0)
const count = (str) => {
  const regex = /[\u4e00-\u9fa5]/g;
  return str.match(regex).length;
}
const initTwikoo = () => {
  const cdnScript = document.createElement('script');
  cdnScript.src = '/js/twikoo/twikoo.all.min.js';
  cdnScript.async = true;

  const loadSecondScript = () => {
    // 执行 twikoo.init() 函数
    const initScript = document.createElement('script');
    initScript.innerHTML = `
      twikoo.init({
        envId: "https://twikoo.xwysyy.cn/",
        el: '#twikoo-comment'
      });
    `;
    initScript.id = 'twikoo-init-id'; // 添加唯一的 ID
    document.body.appendChild(initScript);

  };
  // 在 twikoo js 文件加载完成后，再加载执行 twikoo.init() 函数的 js 文件
  cdnScript.addEventListener('load', loadSecondScript);
  document.body.appendChild(cdnScript);
}
const initPost = () => {

  const backendUrl = "https://ai-post-summary.xwysyy.cn";
  const contentCursor = document.getElementById("contentRef");
  const outputCursor = document.getElementById("post-ai-result-text");
  const loadingText = document.getElementById("result-loading");
  const aiLogoCursor = document.getElementById("ai-logo");
  const postTitleCursor = document.getElementById("summary-post");
  // 获取 postId 和 content
  const url = new URL(location.href);
  const postId = artInfo.title 

  // 处理 backendUrl 可能以 / 结尾的情况
  const apiUrl = backendUrl.endsWith("/")
    ? `${backendUrl}api/summary`
    : `${backendUrl}/api/summary`;
  let content = artInfo.content
  if (content.length > 5000) {
    content = content.substring(0, 2500) + content.substring(content.length - 2500, content.length)
  }
  const requestBody = {
    postId: postId,
    content: content,
  };

  // 光标效果
  const addCursor = () => {
    const cursorSpan = document.createElement("span");
    cursorSpan.className = "ai-cursor";
    outputCursor.appendChild(cursorSpan);
  };

  // 插入光标
  addCursor();

  // 打字效果
  let typingTimeout;
  let shouldDisable = false;

  const typeWriter = (index, text) => {
    if (loadingText) {
      loadingText.remove();
    }
    aiLogoCursor.classList.add("typing");
    if (index < text.length) {
      // 在光标前面插入文本
      const cursor = document.querySelector(".ai-cursor");
      cursor.insertAdjacentText("beforebegin", text.charAt(index));

      index++;

      typingTimeout = setTimeout(() => typeWriter(index, text), 80); // 调整打字速度
    } else {
      aiLogoCursor.classList.remove("typing");
      document.querySelector(".ai-cursor")?.remove(); // 移除光标

      shouldDisable = false;
    }
  };

  // 摘要输入
  const inputSummary = (data) => {
    const outputElement = outputCursor;
    const text = data;
    let index = 0;
    shouldDisable = true;

    clearTimeout(typingTimeout);
    typeWriter(index, text);
  };

  // 发送 GET 请求，获得摘要状态
  let summaryData = undefined;

  const fetchSummary = () => {
    fetch(`${apiUrl}?postId=${postId}`, {
      method: "GET",
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.isSave) {
          // 存入摘要
          summaryData = data.data;
          // 成功，模拟打字效果
          inputSummary(data.data);
        } else {
          // 如果 isSave 为 false，发送 POST 请求
          fetch(apiUrl, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(requestBody),
          })
            .then((response) => response.json())
            .then((data) => {
              if (data.code === 1) {
                // 存入摘要
                summaryData = data.data;
                // 成功，模拟打字效果
                inputSummary(data.data);
              } else {
                // 客户端错误，输出响应内容
                console.error("Error:", data);
              }
            })
            .catch((error) => {
              outputCursor.textContent = `${error}`;
              console.error("Fetch error:", error);
            });
        }
      })
      .catch((error) => {
        outputCursor.textContent = `${error}`;
        console.error("Fetch error:", error);
      });
  };

  // 发送请求
  fetchSummary();

  // 监听 Logo 点击事件
  let switchMode = false;

  aiLogoCursor.addEventListener("click", () => {
    if (!shouldDisable) {
      if (!switchMode) {
        // 介绍模式
        let index = 0;
        const text =
          "我是xwysyy开发的摘要生成助理xwysyyGPT，如你所见，这是一个使用 Qwen 14B 作为生成模型的工具。我在这里只负责显示，并仿照 GPT 的形式输出。";

        shouldDisable = true;
        switchMode = true;

        clearTimeout(typingTimeout);

        outputCursor.textContent = ``;

        addCursor();
        setTimeout(() => {
          typeWriter(index, text);
        }, 1000);
      } else {
        if (summaryData !== undefined) {
          shouldDisable = true;
          switchMode = false;

          clearTimeout(typingTimeout);

          outputCursor.textContent = ``;

          addCursor();
          inputSummary(summaryData);
        }
      }
    }
  });

}
onMounted(() => {
  getArtInfo()
  initTwikoo()
})
// 查询文章
async function getArtInfo() {
  const { data: res } = await http.get(`article/info/${props.id}`)
  artInfo = res.data
  console.log(artInfo)
  countWord.value = count(artInfo.blog)
  window.sessionStorage.setItem('title', artInfo.title)
  date.value = dayjs(res.data.CreatedAt).format('YYYY年MM月DD日 HH:mm')
  artLoaded.value = true
  initPost()
}

</script>

<style scoped>
.Left {
  left: 10px;
}
</style>
