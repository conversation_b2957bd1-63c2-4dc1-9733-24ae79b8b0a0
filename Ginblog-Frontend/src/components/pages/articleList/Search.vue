<template>
  <a-row align="middle">
    <a-col :span="1">
      <FolderOpenTwoTone style="font-size: 30px;" />
    </a-col>
    <a-col :span="23">
      <a-menu v-model:selectedKeys="current" :items="items" mode="horizontal" @click="goToPage" class="cateStyle">
      </a-menu>
    </a-col>
  </a-row>
  <div v-if="total == 0 && isLoad">
    <a-alert message="你搜索的文章标题不存在" type="error" closable style="font-size: 25px;" />
  </div>
  <a-row justify="center">
        <a-col :span="24">
            <a-row :gutter="8">
                <template v-if="windowSizeWidth > 960">
                    <a-col v-for="item in artList" :key="item.ID" :span="12">
                        <div class="cardStyle" @click="router.push(`/article/detail/${item.ID}`)">
                            <a-row >
                                <img :src="item.img" style="height:240px;width:100%; border-radius: 15px 15px 0 0;" />
                            </a-row>
                            <div style="padding: 10px">
                                <a-row>
                                    <span
                                        style="margin-top:0px; margin-bottom: 10px; font-size: 25px; font-weight: 600; border-bottom: 1px solid #9ba2a5;">{{
                                            item.title }}</span>
                                </a-row>
                                <a-row>
                                    <a-space size="middle">
                                        <span><strong>更新时间：</strong>{{ formatDate(item.UpdatedAt) }}</span>
                                        <span><strong>阅读数：</strong>{{ item.read_count }}</span>
                                    </a-space>
                                </a-row>
                            </div>
                        </div>
                    </a-col>
                </template>
                <template v-else>
                    <a-col v-for="item in artList" :key="item.ID" :span="20" :offset="2">
                        <div class="cardStyle" @click="router.push(`/article/detail/${item.ID}`)">
                            <a-row >
                                <img :src="item.img" style="height:220px;width:100%; border-radius: 15px 15px 0 0;" />
                            </a-row>
                            <div style="padding: 10px">
                                <a-row>
                                    <span
                                        style="margin-top:0px; margin-bottom: 10px; font-size: 25px; font-weight: 600; border-bottom: 1px solid #9ba2a5;">{{
                                            item.title }}</span>
                                </a-row>
                                <a-row>
                                    <a-space size="middle">
                                        <span><strong>更新时间：</strong>{{ formatDate(item.UpdatedAt) }}</span>
                                        <span><strong>阅读数：</strong>{{ item.read_count }}</span>
                                    </a-space>
                                </a-row>
                            </div>
                        </div>
                    </a-col>
                </template>
            </a-row>

            <div>
                <a-pagination v-model:current="queryParam.pagenum" show-quick-jumper :total="total" @change="getArtList"
                    style="text-align: center; margin: 25px;">
                    <template #itemRender="{ type, originalElement }">
                        <a v-if="type === 'prev'">Previous</a>
                        <a v-else-if="type === 'next'">Next</a>
                        <component :is="originalElement" v-else></component>
                    </template>
                </a-pagination>
            </div>
        </a-col>
    </a-row>
</template>
<script setup>
import { FolderOpenTwoTone } from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import { onMounted, reactive, ref } from 'vue'
import http from '@/plugin/http'
import router from '@/router';
import { useWindowSize } from '@vueuse/core'
let windowSizeWidth = useWindowSize().width
let artList = ref([''])
let queryParam = reactive({
  pagesize: 8,
  pagenum: 1
})
const current = ref(['index'])
let cateList = reactive([''])
const isLoad = ref(0)
const total = ref(0)

const props = defineProps(['title'])

//格式化时间
const formatDate = (date) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

const items = ref('');
// 获取文章列表
async function getArtList() {
  const { data: res } = await http.get(`article`, {
    params: {
      title: props.title,
      pagesize: windowSizeWidth.value > 960 ? queryParam.pagesize : 6,
      pagenum: queryParam.pagenum
    }
  })
  artList.value = res.data
  total.value = Math.ceil(res.total/queryParam.pagesize)*queryParam.pagesize
  isLoad.value = 1
}
// 获取分类列表
async function GetCateList() {
  const { data: res } = await http.get('category')
  cateList = res.data
  items.value = cateList.map(item => {
    return {
      key: item.id,
      label: item.name
    }
  })
  current.value = []
}
//跳转
const goToPage = (item) => {
  current.value = [item.key]
  router.push('/category/' + item.key).catch((err) => err)
}
onMounted(() => {
  GetCateList();
  getArtList();
})

</script>
<style scoped>
.cardStyle {
    background-color: rgba(255, 255, 255, 0.5);
    margin: 15px;
    text-align: center;
    justify-self: center;
    width: 95%;
    box-shadow: 1px 1px 5px rgb(121, 207, 250);
    border: 1px solid rgb(185, 213, 227);
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.cardStyle::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent, white, transparent);
    transform: translateX(-200%);
    transition: transform .5s linear;
    z-index: 1;
}

.cardStyle:hover::before {
    transform: translateX(100%) skewX(-60deg);
}

.cardStyle:hover {
    transform: scale(1.01);
    transition: transform 0.1s linear;
    border: 1px solid #65b7e9;
}
</style>