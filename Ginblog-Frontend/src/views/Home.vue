<template>
  <div class="background"></div>
  <a-layout>
    <a-layout-header style="background-color:rgba(255, 255, 255,0)">
      <TopBar></TopBar>
    </a-layout-header>
    <a-layout style="background-color:rgba(255, 255, 255,0); " :style="{ 'min-height': windowSizeHeight - 135 + 'px' }">
      <template v-if="windowSizeWidth > 960">
        <a-row align="center">
          <a-col :span="16" :offset="1" class="getWidth">
            <router-view :key="$route.path">
            </router-view>
          </a-col>
          <a-col :span="7">
            <Sidebar></Sidebar>
          </a-col>
        </a-row>
      </template>
      <template v-else>
        <a-row align="center">
          <router-view :key="$route.path">
          </router-view>
        </a-row>
        <a-row align="center">
          <Sidebar></Sidebar>
        </a-row>
      </template>
    </a-layout>
    <a-layout-footer style="background-color:rgba(255, 255, 255,0);">
      <Footer></Footer>
    </a-layout-footer>
  </a-layout>
</template>

<script setup>
import TopBar from '../components/layout/Header.vue'
import Footer from '../components/layout/Footer.vue'
import Sidebar from '../components/layout/Sidebar.vue'
import { useWindowSize } from '@vueuse/core';
let windowSizeWidth = useWindowSize().width
let windowSizeHeight = useWindowSize().height
</script>
