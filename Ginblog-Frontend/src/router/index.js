import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    component: () => import('../components/pages/articleList/ArticleList.vue'),
    meta: { title: '文章列表' },
  },
  {
    path: '/log',
    component: () => import('../components/pages/Log.vue'),
    meta: { title: '日志' },
  },
  {
    path: '/about',
    component: () => import('../components/pages/About.vue'),
    meta: { title: '关于' },
  },
  {
    path: '/githubCalendar',
    component: () => import('../components/project/githubCalendar/index.vue'),
    meta: { title: 'Github贡献热力图' },
  },
  {
    path: '/knowledgeGraph',
    component: () => import('../components/project/neo4j/index.vue'),
    meta: { title: '知识图谱' },
  },
  {
    path: '/friend',
    component: () => import('../components/project/friendCircle/index.vue'),
    meta: { title: '友链朋友圈' },
  },
  {
    path: '/article/detail/:id',
    component: () => import('../components/pages/Details.vue'),
    meta: { title: window.sessionStorage.getItem('title') },
    props: true
  },
  {
    path: '/category/article/detail/:id',
    component: () => import('../components/pages/Details.vue'),
    meta: { title: window.sessionStorage.getItem('title') },
    props: true
  },
  {
    path: '/index',
    component: () => import('../components/pages/articleList/ArticleList.vue'),
    meta: { title: '文章列表' },
  },
  {
    path: '/index.html',
    component: () => import('../components/pages/articleList/ArticleList.vue'),
    meta: { title: '文章列表' },
  },
  {
    path: '/category/:cid',
    component: () => import('../components/pages/articleList/CateList.vue'),
    meta: { title: '分类信息' },
    props: true
  },
  {
    path: '/search/:title',
    component: () => import('../components/pages/articleList/Search.vue'),
    meta: { title: '搜索结果' },
    props: true
  }
]
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

const originalPush = router.push

router.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title
  }
  next()
})

export default router
