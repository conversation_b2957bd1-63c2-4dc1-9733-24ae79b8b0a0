{"name": "front", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@iconify/vue": "^5.0.0", "@vueuse/core": "^10.10.0", "ant-design-vue": "^4.1.2", "axios": "^1.6.7", "echarts": "^5.4.3", "echarts-wordcloud": "^2.1.0", "mavon-editor": "^3.0.1", "moment": "^2.30.1", "pinia": "^2.1.7", "vis-network": "^9.1.9", "vue": "^3.4.15", "vue-jsonp": "^2.0.0", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.3", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "tippy.js": "^6.3.7", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.11", "vite-plugin-compression": "^0.5.1", "vue3-calendar-heatmap": "^2.0.5"}}