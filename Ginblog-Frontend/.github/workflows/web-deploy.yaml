name: web-deploy
on:
  push:
    branches:
      - master

jobs: 
  build-and-deploy:
    runs-on: ubuntu-latest 
    
    steps: 
      - name: Checkout
        uses: actions/checkout@v4
      - name: use node
        uses: actions/setup-node@v4.0.3
        with:
          node-version: 18
      - name: install
        run: npm install
      - name: build
        run: npm run build
      - name: ssh deploy
        uses: easingthemes/ssh-deploy@v5.1.0
            
        with:
          SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
          REMOTE_HOST: ${{ secrets.REMOTE_HOST }}
          REMOTE_USER: ${{ secrets.REMOTE_USER }}
          SOURCE: "/dist/"
          TARGET: "/www/wwwroot/ginblog/front/"
