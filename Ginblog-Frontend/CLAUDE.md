# 开发指南

## 哲学

### 核心信念

- **循序渐进胜过一次性巨变** —— 进行能编译并通过测试的小变动  
- **从既有代码中学习** —— 实现前先研究与规划  
- **务实而非教条** —— 适应项目实际情况  
- **意图清晰胜过炫技** —— 选择朴素、易懂的实现  

### 简单意味着

- 函数 / 类承担单一职责  
- 避免过早抽象  
- 不玩花哨技巧 —— 选最朴素的方案  
- 若需额外解释，则说明设计过于复杂  

## 流程

### 1. 规划与分阶段

将复杂工作拆分为 3–5 个阶段，记录于 `IMPLEMENTATION_PLAN.md`：

`
## 阶段 N：[名称]
**目标**： [具体交付成果]  
**成功标准**： [可测试的结果]  
**测试**： [具体测试用例]  
**状态**： [未开始 | 进行中 | 已完成]
`
- 进展时更新状态  
- 全部阶段完成后删除该文件  

### 2. 实施流程

1. **理解** —— 学习代码库既有模式  
2. **测试** —— 先编写失败测试（红）  
3. **实现** —— 最少代码让测试通过（绿）  
4. **重构** —— 在测试通过前提下清理代码  
5. **提交** —— 提交信息需关联实施计划  

### 3. 遇阻时（最多 3 次尝试）

**关键**：同一问题最多尝试 3 次，然后停止。

1. **记录失败**  
   - 已尝试的方法  
   - 具体错误信息  
   - 失败原因分析  

2. **研究替代方案**  
   - 查找 2–3 个类似实现  
   - 记录不同做法  

3. **质疑基本假设**  
   - 抽象层级是否合适？  
   - 能否拆分成更小问题？  
   - 是否存在更简单途径？  

4. **换个角度尝试**  
   - 使用不同库 / 框架功能？  
   - 采用另一种架构模式？  
   - 与其增加抽象，不如先移除？  

## 技术标准

### 架构原则

- **组合优先于继承** —— 借助依赖注入  
- **接口优先于单例** —— 提升可测试性与灵活性  
- **显式优先于隐式** —— 数据流与依赖清晰可见  
- **尽量测试驱动** —— 切勿跳过或禁用测试  

### 代码质量

- **每次提交必须**  
  - 成功编译  
  - 通过全部现有测试  
  - 为新增功能补充测试  
  - 遵循项目格式 / Lint 规则  

- **提交前**  
  - 运行格式化 / Lint 工具  
  - 自审改动  
  - 在提交信息中说明「为什么」  

### 错误处理

- 尽早失败并给出清晰错误信息  
- 提供调试上下文  
- 在合适层级处理异常  
- 切勿悄悄吞掉错误  

## 决策框架

若存在多种可行方案，按照以下优先级选择：

1. **可测试性** —— 是否易于编写测试？  
2. **可读性** —— 半年后他人能否快速理解？  
3. **一致性** —— 是否符合项目惯例？  
4. **简单性** —— 这是能奏效的最简单方案吗？  
5. **可逆性** —— 今后调整难度如何？  

## 项目集成

### 熟悉代码库

- 找到 3 个类似功能 / 组件  
- 识别常见模式与约定  
- 尽量沿用现有库 / 工具  
- 遵循既有测试结构  

### 工具

- 使用项目现有构建系统  
- 使用项目测试框架  
- 使用项目格式化 / Lint 配置  
- 无充分理由不引入新工具  

## 质量关卡

### 完成判定

- [ ] 测试已编写并全部通过  
- [ ] 代码符合项目规范  
- [ ] 无格式 / Lint 警告  
- [ ] 提交信息清晰  
- [ ] 实现符合实施计划  
- [ ] 无未附问题编号的 TODO  

### 测试指南

- 测行为而非实现细节  
- 尽量单断言  
- 用场景描述性命名  
- 复用现有测试工具 / Helper  
- 测试应可重复、确定性  

## 重要提醒

**绝不**  
- 使用 `--no-verify` 跳过提交钩子  
- 为省事禁用测试  
- 提交无法编译的代码  
- 凭空假设 —— 应以代码为准  

**务必**  
- 递增式提交可工作的代码  
- 随时更新实施计划文档  
- 从现有实现中学习  
- 三次失败后暂停并重新评估