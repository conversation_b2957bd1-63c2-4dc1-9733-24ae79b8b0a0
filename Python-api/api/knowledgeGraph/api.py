from fastapi import APIRouter, HTTPException, Request
from neo4j import GraphDatabase

router = APIRouter()

driver = GraphDatabase.driver("bolt://124.220.59.180:7687", auth=("neo4j", "xwysyy1234")) 

@router.post("/data")
async def get_data(request: Request):
    data = await request.json()
    node_name = data.get("data")

    if not node_name:
        raise HTTPException(status_code=400, detail="Missing 'data' field in request")

    with driver.session() as session:
        result = session.run('MATCH (n {name: $name})-[r]-(m) RETURN n,r,m', name=node_name)

        nodes = []
        links = []
        node_ids = set()

        for record in result:
            node1 = record['n']
            node2 = record['m']
            link = record['r']

            nodes.append({
                "id": node1.element_id,
                "label": node1['name']
            })

            nodes.append({
                "id": node2.element_id,
                "label": node2['name']
            })

            links.append({
                "id": link.element_id,
                "from": node1.element_id,
                "to": node2.element_id,
                "label": link.type,
                "color": 'black',
                "width": 1,
                "title": link.get('description', '')
            })

            node_ids.add(node1.element_id)
            node_ids.add(node2.element_id)

    nodes = [dict(t) for t in {tuple(d.items()) for d in nodes}]

    return {"nodes": nodes, "links": links}

@router.get("/item")
async def get_item():
    with driver.session() as session:
        result = session.run('MATCH (n)-[r]-(m) RETURN n,r,m LIMIT 300')

        nodes = []
        node_ids = set()

        for record in result:
            node1 = record['n']
            node2 = record['m']

            nodes.append({
                "id": node1.element_id,
                "label": node1['name']
            })

            nodes.append({
                "id": node2.element_id,
                "label": node2['name']
            })

            node_ids.add(node1.element_id)
            node_ids.add(node2.element_id)

    # 去重
    nodes = [dict(t) for t in {tuple(d.items()) for d in nodes}]

    return {"nodes": nodes}
