from fastapi import APIRouter, Query, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
import random
import os
import csv
import httpx

router = APIRouter()

# 是否开启 raw 选项
ALLOW_RAW_OUTPUT = False

# 读取 url.csv 文件
def read_url_csv():
    """
    读取 url.csv 文件并返回 URL 列表
    """
    urls = []
    with open("api/img/url.csv", "r", encoding="utf-8") as file:
        reader = csv.reader(file)
        for row in reader:
            if row and row[0].strip():
                urls.append(row[0].strip())
    
    # 如果文件不存在，返回默认 URL
    if not urls:
        urls = ["https://http.cat/503"]
    return urls

# 获取 URL 列表
imgs_array = read_url_csv()

@router.get("/")
async def get_image(
    id: int = Query(None, description="指定图片 ID"),
    json: bool = Query(False, description="返回 JSON 格式"),
    raw: bool = Query(False, description="返回原始图片数据"),
    response: Response = None
):
    """
    获取图片 URL 或原始图片数据
    """
    # 处理 ID
    if id is not None:
        if id < 0 or id >= len(imgs_array):
            id = random.randint(0, len(imgs_array) - 1)
        else:
            response.headers["Cache-Control"] = "public, max-age=86400"
    else:
        id = random.randint(0, len(imgs_array) - 1)
        response.headers["Cache-Control"] = "no-cache"

    # 返回 JSON 格式
    if json:
        return {"id": id, "url": imgs_array[id]}

    # 返回原始图片数据
    if raw:
        if not ALLOW_RAW_OUTPUT:
            raise HTTPException(status_code=403, detail="Raw output is not allowed")
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(imgs_array[id])
                response.raise_for_status()
                return Response(content=response.content, media_type="image/png")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to fetch image: {str(e)}")

    # 重定向到图片 URL
    response.headers["Referrer-Policy"] = "no-referrer"
    
    
    return Response(status_code=302, headers={"Location": imgs_array[id]})
