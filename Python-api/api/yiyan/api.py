from fastapi import APIRouter, HTTPException, Request
import random
from datetime import datetime

router = APIRouter()

@router.get("/")
async def get_yiyan(charset: str = "utf-8"):
    """
    获取随机一言
    """
    try:
        # 读取文件内容
        try:
            with open("api/yiyan/yiyan.txt", "r", encoding="utf-8") as file:
                lines = file.readlines()
                lines = [line.strip() for line in lines] 
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"读取文件失败: {str(e)}")
        if not lines:
            raise HTTPException(status_code=404, detail="文件内容为空")

        # 随机选择一行
        index = random.randint(0, len(lines) - 1)
        content = lines[index]

        # 编码转换
        if charset.lower() == "gbk":
            content = content.encode("gbk", errors="ignore").decode("gbk")
        else:
            charset = "utf-8"

        # 返回结果
        return {
            "data": {
                "id": index,
                "content": content
            },
            "status": 200,
            "msg": "数据获取成功！",
            "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")