from fastapi import HTTPException, Query, APIRouter
import httpx
import random
from datetime import datetime
import json
import re

async def fetch_url(url, headers=None, cookies=None, referer=None):
    """
    发送 HTTP 请求并返回响应内容
    """
    try:
        async with httpx.AsyncClient() as client:
            headers = headers or {}
            if referer:
                headers["Referer"] = referer
            response = await client.get(url, headers=headers, cookies=cookies)
            response.raise_for_status()
            return response.text
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"请求失败: {str(e)}")

class Api:
    """
    聚合热搜榜 API
    """

    # 少数派 热榜
    async def sspai(self):
        url = "https://sspai.com/api/v1/article/tag/page/get?limit=100000&tag=%E7%83%AD%E9%97%A8%E6%96%87%E7%AB%A0"
        referer = "https://sspai.com"
        response = await fetch_url(url, referer=referer)
        json_res = json.loads(response)
        temp_arr = []

        for k, v in enumerate(json_res["data"]):
            temp_arr.append({
                "index": k + 1,
                "title": v["title"],
                "createdAt": datetime.fromtimestamp(v["released_time"]).strftime("%Y-%m-%d"),
                "other": v["author"]["nickname"],
                "like_count": v["like_count"],
                "comment_count": v["comment_count"],
                "url": f"https://sspai.com/post/{v['id']}",
                "mobilUrl": f"https://sspai.com/post/{v['id']}"
            })

        return {
            "success": True,
            "title": "少数派",
            "subtitle": "热榜",
            "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "data": temp_arr
        }

    # CSDN 头条榜
    async def csdn(self):
        url = "https://www.csdn.net"
        headers = {
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.30 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1"
        }
        referer = "https://www.csdn.net"
        response = await fetch_url(url, headers=headers, referer=referer)
        match = re.search(r'window\.__INITIAL_STATE__=(.*?);</script>', response)
        if not match:
            raise HTTPException(status_code=500, detail="解析 CSDN 数据失败")

        json_res = json.loads(match.group(1))
        temp_arr = []

        # 头条
        for k, v in enumerate(json_res["pageData"]["data"]["Headimg"]):
            temp_arr.append({
                "index": k + 1,
                "title": v["title"],
                "url": v["url"],
                "mobilUrl": v["url"]
            })

        # 头条1
        for k, v in enumerate(json_res["pageData"]["data"]["www-Headlines"]):
            temp_arr.append({
                "index": k + 17,
                "title": v["title"],
                "url": v["url"],
                "mobilUrl": v["url"]
            })

        # 头条2
        for k, v in enumerate(json_res["pageData"]["data"]["www-headhot"]):
            temp_arr.append({
                "index": k + 48,
                "title": v["title"],
                "url": v["url"],
                "mobilUrl": v["url"]
            })

        return {
            "success": True,
            "title": "CSDN",
            "subtitle": "头条榜",
            "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "data": temp_arr
        }

    # 百度百科 历史上的今天
    async def history(self):
        month = datetime.now().strftime("%m")
        day = datetime.now().strftime("%d")
        url = f"https://baike.baidu.com/cms/home/<USER>/{month}.json"
        referer = "https://baike.baidu.com"
        response = await fetch_url(url, referer=referer)
        json_res = json.loads(response)
        temp_arr = []

        for k, v in enumerate(json_res[month][f"{month}{day}"]):
            temp_arr.append({
                "index": k + 1,
                "title": f"{v['year']}年-{v['title'].strip()}",
                "url": f"https://www.douyin.com/search/{v['title']}",
                "mobilUrl": f"https://www.douyin.com/search/{v['title']}"
            })

        return {
            "success": True,
            "title": "百度百科",
            "subtitle": "历史上的今天",
            "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "data": temp_arr
        }

    # 抖音 热搜榜
    async def douyin(self):
        url = "https://www.iesdouyin.com/web/api/v2/hotsearch/billboard/word/"
        referer = "https://www.douyin.com"
        response = await fetch_url(url, referer=referer)
        json_res = json.loads(response)
        temp_arr = []

        for k, v in enumerate(json_res["word_list"]):
            temp_arr.append({
                "index": k + 1,
                "title": v["word"],
                "hot": f"{round(v['hot_value'] / 10000, 1)}万",
                "url": f"https://www.douyin.com/search/{v['word']}",
                "mobilUrl": f"https://www.douyin.com/search/{v['word']}"
            })

        return {
            "success": True,
            "title": "抖音",
            "subtitle": "热搜榜",
            "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "data": temp_arr
        }

    # 哔哩哔哩 全站日榜
    async def bilibili_rankall(self):
        url = "https://api.bilibili.com/x/web-interface/ranking/v2?rid=0&type=all"
        referer = "https://www.bilibili.com"

        response = await fetch_url(url, referer=referer)
        json_res = json.loads(response)
        temp_arr = []

        for k, v in enumerate(json_res["data"]["list"]):
            temp_arr.append({
                "index": k + 1,
                "title": v["title"],
                "pic": v["pic"],
                "desc": v["desc"],
                "hot": f"{round(v['stat']['view'] / 10000, 1)}万",
                "url": v["short_link"],
                "mobilUrl": v["short_link"]
            })

        return {
            "success": True,
            "title": "哔哩哔哩",
            "subtitle": "全站日榜",
            "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "data": temp_arr
        }

    # 哔哩哔哩 热搜榜
    async def bilibili_hot(self):
        url = "https://app.bilibili.com/x/v2/search/trending/ranking"
        referer = "https://www.bilibili.com"
        response = await fetch_url(url, referer=referer)
        json_res = json.loads(response)
        temp_arr = []

        for v in json_res["data"]["list"]:
            temp_arr.append({
                "index": v["position"],
                "title": v["keyword"],
                "url": f"https://search.bilibili.com/all?keyword={v['keyword']}&order=click",
                "mobilUrl": f"https://search.bilibili.com/all?keyword={v['keyword']}&order=click"
            })

        return {
            "success": True,
            "title": "哔哩哔哩",
            "subtitle": "热搜榜",
            "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "data": temp_arr
        }

    # 知乎热榜 热度
    async def zhihuHot(self):
        url = "https://www.zhihu.com/api/v3/feed/topstory/hot-lists/total?limit=50&desktop=true"
        referer = "https://www.zhihu.com"
        response = await fetch_url(url, referer=referer)
        json_res = json.loads(response)
        temp_arr = []

        for k, v in enumerate(json_res["data"]):
            hot = re.search(r"\d+", v["detail_text"]).group()
            temp_arr.append({
                "index": k + 1,
                "title": v["target"]["title"],
                "hot": f"{hot}万",
                "url": f"https://www.zhihu.com/question/{v['target']['id']}",
                "mobilUrl": f"https://www.zhihu.com/question/{v['target']['id']}"
            })

        return {
            "success": True,
            "title": "知乎热榜",
            "subtitle": "热度",
            "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "data": temp_arr
        }

    # 微博 热搜榜
    async def wbresou(self):
        url = "https://weibo.com/ajax/side/hotSearch"
        cookies = {"Cookie": f"{random.getrandbits(128)}:FG=1"}
        referer = "https://s.weibo.com"
        response = await fetch_url(url, cookies=cookies, referer=referer)
        json_res = json.loads(response)
        temp_arr = []

        for k, v in enumerate(json_res["data"]["realtime"]):
            temp_arr.append({
                "index": k + 1,
                "title": v["note"],
                "hot": f"{round(v['num'] / 10000, 1)}万",
                "url": f"https://s.weibo.com/weibo?q={v['note']}&Refer=index",
                "mobilUrl": f"https://s.weibo.com/weibo?q={v['note']}&Refer=index"
            })

        return {
            "success": True,
            "title": "微博",
            "subtitle": "热搜榜",
            "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "data": temp_arr
        }

    # 百度热点 指数
    async def baiduredian(self):
        url = "https://top.baidu.com/board?tab=realtime"
        response = await fetch_url(url)
        match = re.search(r'<!--s-data:(.*?)-->', response)
        if not match:
            raise HTTPException(status_code=500, detail="解析百度热点数据失败")

        json_res = json.loads(match.group(1))
        temp_arr = []

        for v in json_res["data"]["cards"]:
            for k, _v in enumerate(v["content"]):
                temp_arr.append({
                    "index": k + 1,
                    "title": _v["word"],
                    "desc": _v["desc"],
                    "pic": _v["img"],
                    "url": _v["url"],
                    "hot": f"{round(int(_v['hotScore']) / 10000, 1)}万",
                    "mobilUrl": _v["appUrl"]
                })

        return {
            "success": True,
            "title": "百度热点",
            "subtitle": "指数",
            "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "data": temp_arr
        }
    
api = Api()
router = APIRouter()

@router.get("/")
async def get_hot(type: str = Query(..., description="API 类型")):
    """
    获取热搜数据
    """
    if type == '"baidu"' or type == "baidu":
        return await api.baiduredian()
    elif type == "zhihu" or type == '"zhihu"':
        return await api.zhihuHot()
    elif type == "weibo" or type == '"weibo"':
        return await api.wbresou()
    # elif type == "bilihot" or type == '"bilihot"':
    #     return await api.bilibili_hot()
    # elif type == "biliall" or type == '"biliall"':
    #     return await api.bilibili_rankall()
    elif type == "douyin" or type == '"douyin"':
        return await api.douyin()
    # elif type == "csdn" or type == '"csdn"':
    #     return await api.csdn()
    elif type == "history" or type == '"history"':
        return await api.history()
    elif type == "sspai" or type == '"sspai"':
        return await api.sspai()
    else:
        raise HTTPException(status_code=400, detail="参数不完整")
