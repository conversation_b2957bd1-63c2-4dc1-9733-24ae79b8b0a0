from fastapi import APIRouter, HTTPException, Request
import httpx

router = APIRouter()

# 微博热搜的类别映射
jyzy = {
    '电影': '影',
    '剧集': '剧',
    '综艺': '综',
    '音乐': '音'
}

@router.get("/")
async def weibo():
    """
    返回微博热搜数据
    """
    url = "https://weibo.com/ajax/side/hotSearch"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Referer": "https://weibo.com/",
        "Accept-Language": "en-US,en;q=0.9",
    }
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, headers=headers)
            response.raise_for_status()  # 检查请求是否成功
            data_json = response.json()["data"]["realtime"]
            data_list = []

            for item in data_json:
                # 跳过广告
                if "is_ad" in item:
                    continue

                # 判断热搜类型
                hot = ""
                if "flag_desc" in item and item["flag_desc"] in jyzy:
                    hot = jyzy[item["flag_desc"]]
                if "is_boom" in item:
                    hot = "爆"
                if "is_hot" in item:
                    hot = "热"
                if "is_fei" in item:
                    hot = "沸"
                if "is_new" in item:
                    hot = "新"

                # 构造热搜条目
                dic = {
                    "title": item["note"],
                    "url": f"https://s.weibo.com/weibo?q=%23{item['word']}%23",
                    "num": item["num"],
                    "hot": hot
                }
                data_list.append(dic)

            return data_list
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取微博热搜失败: {str(e)}")
