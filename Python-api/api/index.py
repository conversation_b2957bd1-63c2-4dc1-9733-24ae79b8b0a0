from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from api.knowledgeGraph.api import router as graph_router
from api.weibo.api import router as weibo_router
from api.yiyan.api import router as yiyan_router
from api.hotlist.api import router as hotlist_router
from api.img.api import router as img_router

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  
    allow_credentials=True,
    allow_methods=["*"],  
    allow_headers=["*"], 
)

app.include_router(graph_router, prefix="/knowledgeGraph")
app.include_router(weibo_router, prefix="/weibo")
app.include_router(yiyan_router, prefix="/yiyan")
app.include_router(hotlist_router, prefix="/hotlist")
app.include_router(img_router, prefix="/img")

if __name__ == '__main__':
    import uvicorn
    uvicorn.run(app)